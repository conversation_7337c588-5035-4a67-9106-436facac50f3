import request from '@/config/axios'

export const addBc = async (data) => {
  return await request.post({ url: '/system/attendance/manager/shift/add', data })
}

export const getListData = async (params) => {
  return await request.get({ url: '/system/attendance/manager/shift/list',params })
}

export const addOrUpdate = async (data) => {
  return await request.post({ url: '/system/attendance/manager/group/addOrUpdate', data })
}


export const groupList = async (params) => {
  return await request.get({ url: '/system/attendance/manager/group/list', params })
}

export const deleteBc = async (id) => {
  return await request.delete({ url: '/system/attendance/manager/shift/delete?id=' + id })
}

export const deleteKq = async (id) => {
  return await request.delete({ url: '/system/attendance/manager/group/delete?id=' + id })
}

export const groupKqList = async (id) => {
  return await request.get({ url: '/system/attendance/manager/group/get?id='+ id })
}