// import request from '@/config/axios/i2'
import request from '@/config/axios'
// import { syncRequest } from '@/config/axios/service'

/**
 *
 * 查询当前用户的指定级别领导
 * @param level 指定级别
 * @param deptId 部门ID
 * @param skipEmpty 是否跳过空部门
 * @returns {Promise<unknown>}
 */
export const getUserLeader = async (level, deptId, skipEmpty) => {
  return await request.get2({
    url: `/wflow/process/step/leader/level`,
    params: { level: level, deptId: deptId, skipEmpty: skipEmpty }
  })
}
/**
 * 查询当前用户的指定级别下的所有领导
 * @param maxLevel 截至级别，为空则直到顶级
 * @param deptId 部门ID
 * @param skipEmpty 是否跳过空部门
 */
export const getUserLeaders = async (maxLevel, deptId, skipEmpty) => {
  return await request.get2({
    url: `/wflow/process/step/leader/to/level`,
    params: { maxLevel: maxLevel, deptId: deptId, skipEmpty: skipEmpty }
  })
}
/**
 * 获取指定系统角色的所有人员
 * @param roles
 */
export const getUsersByRoles = async (roles) => {
  return await request.post2({
    url: `/wflow/process/step/userByRoles`,
    data: roles
  })
}
/**
 * 查询所有部门主管
 * @param deptIds 部门ID
 */
export const getLeaderByDepts = async (deptIds) => {
  return await request.post2({
    url: `/wflow/process/step/deptLeader`,
    data: deptIds
  })
}
/**
 * 获取指定人员是否属于某部门
 * @param userId 该人员ID
 * @param depts 部门 List
 */
// export function userInDepts(userId, depts){
//   let result = false
//   syncRequest({
//     url: `/wflow/process/step/user/${userId}/belong/depts`,
//     method: 'post2',
//     data: depts,
//     type: 'json',
//     success: res => {
//       result = res
//     }
//   })
//   return result
// }

/**
 * 获取指定的多个人员是否属于某些部门
 * @param userIds 该人员ID集合
 * @param depts 部门 List
 */
// export function usersInDepts(userIds, depts){
//   let result = false
//   syncRequest({
//     url: `/wflow/process/step/users/belong/depts`,
//     method: 'post2',
//     data: {
//       sourceIds: userIds,
//       targetIds: depts
//     },
//     type: 'json',
//     success: res => {
//       result = res
//     }
//   })
//   return result
// }
/**
 * 获取指定的多个部门是否属于某些部门子部门
 * @param depts 该部门ID集合
 * @param parents 父部门 List
 */
// export function deptsInDepts(depts, parents){
//   let result = false
//   syncRequest({
//     url: `/wflow/process/step/depts/belong/depts`,
//     method: 'post2',
//     data: {
//       sourceIds: depts,
//       targetIds: parents
//     },
//     type: 'json',
//     success: res => {
//       result = res
//     }
//   })
//   return result
// }

export const getNewVerProcess = async (code, isSub = false) => {
  return await request.get2({
    url: `/wflow/process/model${isSub ? '/sub' : ''}/${code}`,
  })
}
export const saveProcess = async (params, isSub = false) => {
  return await request.post2({
    url: `/wflow/process/model${isSub ? '/sub' : ''}/save/`,
    data: params
  })
}
export const deployProcess = async (code, isSub = false,formType = 1) => {
  return await request.post2({
    url: `/wflow/process/model${isSub ? '/sub' : ''}/deploy/${code}`+'?formType='+formType,
  })
}
export const getCustomPrintConfig = async (instanceId) => {
  return await request.get2({
    url: `/wflow/process/model/customPrint/${instanceId}`,
  })
}
export const delProcessInst = async (instanceId) => {
  return await request.delete({
    url: `/wflow/process/instance/${instanceId}`,
  })
}
export const startProcess = async (code, params) => {
  return await request.post2({
    url: `/wflow/process/start/${code}`,
    data: params
  })
}
//查询条件节点的所有满足的条件分支
export const getTrueConditions = async (data) => {
  return await request.post2({
    url: `/wflow/process/step/conditions/resolve`,
    data
  })
}
//校验EL表达式
export const validateEl = async (el) => {
  return await request.get2({
    url: `/wflow/process/step/el/validate`,
    params: { el: el }
  })
}
export const saveUserSign = async (data) => {
  return await request.post2({
    url: `/wflow/process/task/save/userSign`,
    data
  })
}
export default {
  getUserLeader, getUserLeaders, getUsersByRoles, validateEl,
  deployProcess, saveProcess, getNewVerProcess, getCustomPrintConfig,
  getLeaderByDepts, delProcessInst, getTrueConditions,saveUserSign
}
