// import request from '@/config/axios/i2'
import request from '@/config/axios'
// 获取表单分组
export const login = async (userId) => {
  return await request.get2({
    url: `/sys/auth/login/ignore/${userId}`,
  })
}

export const getTenant = async () => {
  return await request.get2({
    url: `/system/tenant/me/list`,
  })
}
export const bindTenant = async (params) => {
  return await request.post2({
    url: `/system/tenant/me/bind`,
    params
  })
}
