import request from '@/config/axios'

// 消息列表
export const getUserConvs= async (params) => {
  return await request.get2({ url: `/system/message/get-user-convs`,params})
}
// 消息详情
export const getTargetConvs= async (params) => {
  return await request.get2({ url: `/system/message/get-target-conv`,params})
}
// 发送消息
export const messageSend= async (data) => {
  return await request.post2({ url: `/system/message/send`,data})
}
// 获取群成员名单
export const groupMembers= async (params) => {
  return await request.get2({ url: `/system/message/get-group-members`,params})
}
// 获取群消息已读未读人员明细
export const readMembers= async (params) => {
  return await request.get2({ url: `/system/message/get-read-user`,params})
}
// 获取群消息已读未读人员明细
export const sendRead= async (params) => {
  return await request.get2({ url: `/system/message/sendread`,params})
}
// 上传图片/附件
export const uploadFile= async (data) => {
  return await request.upload({ url: `/wflow/res`,data})
}
// 消息置顶
export const listTopSet= async (params) => {
  return await request.get2({ url: `/system/message/top-set`,params})
}
// 消息置顶
export const listMuteSet= async (params) => {
  return await request.get2({ url: `/system/message/mute-set`,params})
}
// 消息撤回
export const detailMsgRecall= async (params) => {
  return await request.get2({ url: `/system/message/recall`,params})
}
// 获取用户详情数据
export const getUserInfo= async (params) => {
  return await request.get2({ url: `/system/user/get-current`,params})
}

// 群组详情
export const getMessageGroupInfo= async (params) => {
  return await request.get2({ url: `/system/message/group/info`,params})
}

// 创建群组
export const createMessageGroup= async (data) => {
  return await request.post2({ url: `/system/message/group/create`,data})
}

// 添加群组成员
export const addMessageGroupMember= async (data) => {
  return await request.post2({ url: `/system/message/group/member/add`,data})
}


// 获取租户用户列表（新增员工移除员工创建群组用）可用群组id查询
export const getListByTenan = async (params) => {
  return await request.get2({
    url: `/system/user/tenan/user/list`,
    params
  })
}

// 根据群id查找人员列表
export const getMemberListByGroupId = async (params) => {
  return await request.get2({
    url: `/system/message/group/member/list`,
    params
  })
}

// 移除群组成员
export const delMessageGroupMember= async (data) => {
  return await request.post2({ url: `/system/message/group/member/del`,data})
}


// 退出群聊
export const exitMessageGroup= async (params) => {
  return await request.get2({ url: `/system/message/quit`,params})
}

// 解散群组
export const dismissMessageGroup= async (data) => {
  return await request.post2({ url: `/system/message/group/del`,data})
}

// 修改群组信息
export const updateMessageGroupInfo= async (data) => {
  return await request.post2({ url: `/system/message/group/modify`,data})
}


// 获取最新的入职签约链接
export const getApplicationKeyContractNewUrl = async (params) => {
  return await request.get2({
    url: `/banbabn/application-key/contract/new/url`,
    params
  })
}

// 查询总角标 socket推送过来
export const getUserMessageTotal = async (params) => {
  return await request.get2({
    url: '/system/user/message/total',
    params
  })
}

// 根据消息id获取单条消息体详情
export const getImMessageById = async (params) => {
  return await request.get2({
    url: `/system/message/im/message/get`,
    params
  })
}