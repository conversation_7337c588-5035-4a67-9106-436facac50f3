// import request from '@/config/axios/i2'
import request from '@/config/axios'

//获取用户待办
export const getUserTodoList = async (params) => {
  return await request.get2({
    url: `/wflow/process/task/todoList`,
    params
  })
}
export const mySubmittedExport = async (params) => {
  return await request.download({
    url: `/wflow/process/mySubmittedExport`,
    params
  })
}
//获取用户发起的实例
export const getUserSubmittedList = async (params) => {
  return await request.get2({
    url: `/wflow/process/mySubmitted`,
    params
  })
}
//获取所有发起的实例
export const getSubmittedList = async (params) => {
  return await request.get2({
    url: `/wflow/process/submittedList`,
    params
  })
}

export const processIdoExport = async (params) => {
  return await request.download({
    url: `/wflow/process/task/idoExport`,
    params
  })
}
//获取我已处理的所有审批实例
export const getIdoList = async (params) => {
  return await request.get2({
    url: `/wflow/process/task/idoList`,
    params
  })
}
//查询流程进度及表单
export const getFormAndProcessProgress = async (instanceId, nodeId) => {
  return await request.get2({
    url: `/wflow/process/progress/${instanceId}/${nodeId}`
  })
}
//处理任务
export const approvalTask = async (data) => {
  return await request.post2({
    url: `/wflow/process/task/handler`,
    data
  })
}
//获取流程实例表单数据
export const getInstanceFormData = async (instanceId) => {
  return await request.get2({
    url: `/wflow/process/form/data/by/${instanceId}`
  })
}
//获取可回退的节点
export const getEnableRecallNodes = async (instanceId, taskId) => {
  return await request.get2({
    url: `/wflow/process/task/recall/nodes`,
    params: { instanceId: instanceId, taskId: taskId }
  })
}
export const getTaskNodeSettings = async (taskId) => {
  return await request.get2({
    url: `/wflow/process/task/settings/${taskId}`,
  })
}
// 审批详情转发
export const processShare = async (instanceId,data) => {
  return await request.get2({
    url: `wflow/process/share/${instanceId}`,
    method: 'post',
    data
  })
}
// 发起-查看数据
export const getProcessData = async (params) => {
  return await request.get2({
    url: `/wflow/process/getData`,
    params
  })
}
export const processExport = async (params) => {
  return await request.download({
    url: `/wflow/process/export`,
    params
  })
}
// 草稿箱列表
export const getDraftsPage = async (params) => {
  return await request.get2({
    url: `/oa/pro-drafts/page`,
    params
  })
}
// 新增-草稿箱
export const getDraftsCreate = async (data) => {
  return await request.post2({
    url: `/oa/pro-drafts/create`,
    data
  })
}
// 更新-草稿箱
export const getDraftsUpdate = async (data) => {
  return await request.put2({
    url: `/oa/pro-drafts/update`,
    data
  })
}
// 获取-草稿箱
export const getDraftsDetail = async (params) => {
  return await request.get2({
    url: `/oa/pro-drafts/get`,
    params
  })
}
// 删除-草稿箱
export const getDraftsDelete = async (params) => {
  return await request.delete2({
    url: `/oa/pro-drafts/delete`,
    params
  })
}
export const groupModelList =  (params) => {
  return request.get2({
    url: '/wflow/model/group/simple-list',
    params
  })
}

export const groupTenantList =  () => {
  return request.get2({
    url: '/system/tenant/simple-list',
  })
}
// 查询是否可删除
export const deleteFlag =  (params) => {
  return request.get2({
    url: '/wflow/process/task/comment/deleteflag',
    params
  })
}
export const commentDelete =  (params) => {
  return request.delete2({
    url: '/wflow/process/task/comment/delete',
    params
  })
}
// 批量审核 待处理
export const batchHandler =  (data) => {
  return request.post2({
    url: '/wflow/process/task/batch/handler',
    data
  })
}
// 批量删除流程实例
export const instanceBatchDelDelete =  (data) => {
  return request.post2({
    url: '/wflow/process/instance/batchDel',
    data
  })
}


export default {
  getUserTodoList,
  getUserSubmittedList,
  getSubmittedList,
  getFormAndProcessProgress,
  approvalTask,
  getInstanceFormData,
  getEnableRecallNodes,
  getIdoList,
  getTaskNodeSettings,
  processShare,
  getProcessData,
  processExport,
  getDraftsPage,
  getDraftsCreate,
  getDraftsDetail,
  getDraftsUpdate,
  getDraftsDelete,
  groupModelList,
  groupTenantList,
  deleteFlag,
  commentDelete,
  batchHandler,
  instanceBatchDelDelete,
  processIdoExport,
  mySubmittedExport
}
