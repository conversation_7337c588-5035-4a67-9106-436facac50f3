<template>
  <el-card v-loading="loading" class="formsBox oaCard">
    <el-scrollbar style="height:calc(100vh - 160px)!important;">
        <iframe ref="myIframe" :src="url" frameborder="0" style="width: 100%; height: 100vh" />
    </el-scrollbar>
  </el-card>
</template>

<script>
export default {
  name: 'talentBoLeLink',
  data() {
    return {
      loading: false,
      width: '57%',
      screenWidth: null,
      url: ''
    }
  },
  computed: {
    isMobile() {
      return window.screen.width < 450
    }
  },
  watch: {
    screenWidth: function (n, o) {
      if (n > 1600) {
        this.width = '57%'
      }
      if (n <= 1600) {
        this.width = '70%'
      }
      if (n <= 1200) {
        this.width = '80%'
      }
      if (n <= 700) {
        this.width = '95%'
      }
    }
  },
  mounted() {
    // console.log(this.$route.params.id)
    // console.log(this.$route.query.link)
    this.url = decodeURIComponent(this.$route.query.link)
    this.$route.meta.title = this.$route.query.names;
    this.screenWidth = document.body.clientWidth
    window.onresize = () => {
      this.screenWidth = document.body.clientWidth
    }
    // this.url = decodeURIComponent(this.$route.query.link)
  },

  methods: {}
}
</script>

<style lang="less" scoped>
.formsBox {
  position: relative;
}
</style>