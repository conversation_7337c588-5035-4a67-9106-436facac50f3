<template>
  <div>
    <el-table :data="tableData" v-loading="loading" @row-click="showProcess">
      <el-table-column label="序号" width="100px" type="index" align="center" />
      <el-table-column prop="formName" label="标题" width="450">
        <template #default="scope">
          {{ $wflow.loginUser.name }}提交的{{ scope.row.formName }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="更新时间">
        <template #default="scope">
          {{ toTime(scope.row.updateTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button @click="handleEdit(scope.row.id)" type="text">继续编辑</el-button>
          <el-button @click="handleDelete(scope.row.id)" type="text">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="text-align: right">
      <el-pagination background :page-sizes="[10, 20, 30, 50, 100]" layout="total, sizes,prev, pager, next,jumper"
        :total="total" :page-size="params.pageSize" v-model:current-page="params.pageNo"
        @size-change="handleSizeChange" />
    </div>
  </div>
</template>

<script>
import taskApi from '@/api/processTask'
const message = useMessage()
export default {
  components: {

  },
  data() {
    return {
      loading: false,
      total: 0,
      params: {
        pageSize: 10,
        pageNo: 1,
      },
      tableData: [],
      processVisible: false,
      formId: ''
    }
  },
  computed: {
    isMobile() {
      return window.screen.width < 450
    },
  },
  watch: {
    params: {
      deep: true,
      handler() {
        this.getList()
      },
    },
  },
  methods: {
    getId(e) {
      console.log(e)
      this.total = 0
      this.tableData = []
      this.formId = e
      this.getList()
    },
    getList() {
      this.loading = true
      taskApi.getDraftsPage({
        ...this.params,
        creator: this.$wflow.loginUser.id,
        formId: this.formId,
      }).then((res) => {
        this.loading = false
        this.total = res.data.total
        this.tableData = res.data.list
      }).catch((e) => {
        this.loading = false
      })
    },
    handleEdit(id) {
      this.$emit('edit', id)
    },
    async handleDelete(id) {
      await message.delConfirm()
      await taskApi.getDraftsDelete({ id: id })
      message.success('操作成功')
      this.getList()
    },
    toTime(e) {
      const date = new Date(e);
      const Y = date.getFullYear() + '-';
      const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
      const D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()) + ' ';
      const h = (date.getHours() < 10 ? '0' + (date.getHours()) : date.getHours()) + ':';
      const m = (date.getMinutes() < 10 ? '0' + (date.getMinutes()) : date.getMinutes());
      const time1 = Y + M + D + h + m
      return time1
    }
  },
  emits: ['edit'],
}
</script>

<style scoped lang="less">
.el-pagination {
  justify-content: right;
  margin-top: 20px;
}
</style>