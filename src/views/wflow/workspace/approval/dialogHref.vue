<template>
  <!-- a标签连接预览 -->
  <Dialog width="1200" title="预览" append-to-body class="file-custom-dialog">
    <iframe :src="url" class="iframeDiv"></iframe>
  </Dialog>
</template>

<script>
export default {
  props: {
    visible: false,
  },
  data() {
    return {
      url: ''
    }
  },
  methods: {
    getUrl(e) {
      console.log(e)
      this.url = e
    }
  }
}
</script>

<style lang="less" scoped>
.iframeDiv {
  width: 100%;
  height: 100%;
  border: medium none;
}
</style>
<style>
.file-custom-dialog {
  height: 750px;

}

.file-custom-dialog .el-dialog__body {
  height: calc(100% - 85px);
}
</style>
