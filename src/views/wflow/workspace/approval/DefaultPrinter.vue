<template>
  <div id="default-print" class="print">
    <h2>{{ instance.processDefName }}</h2>
    <div class="header">
      <div>{{ instance.tenantName }}</div>
      <div>审批编号：{{ instance.instanceId }}</div>
      <div>创建时间：{{ instance.startTime }}</div>
    </div>

    <!-- <div class="qr-code">
      <div>扫码查流程</div>
      <qrcode-vue ref="qrCode" :value="qrCode" :size="90" level="L"/>
    </div> -->
    <div class="content">
      <img class="result" v-if="status.img" :src="status.img" />
      <table border="0">
        <tr>
          <th>提交人</th>
          <td>{{ instance.staterUser.name }}</td>
        </tr>
        <tr>
          <th>所在部门</th>
          <td>{{ instance.starterDept }}</td>
        </tr>

        <template v-for="form in formItems" :key="form.id">
          <tr v-if="!form.child">
            <th>{{ form.title }}</th>
            <td>
              <default-print-item :config="form" :data="instance.formData[form.id]"
                v-if="form.name !== 'Description'" />
              <div v-else>{{ form.props.placeholder }}</div>
            </td>
          </tr>
          <template v-for="(item, index) in form.child" :key='index'>
            <tr>
              <th>{{ item.title }}</th>
              <td>
                <default-print-item :config="item" :data="instance.formData[item.id]"
                  v-if="item.name !== 'Description'" />
                <div v-else>{{ item.props.placeholder }}</div>
              </td>
            </tr>
          </template>
        </template>
        <tr>
          <th :rowspan="getName ? process.length + 2 : process.length + 1">审批流程</th>
        </tr>
        <tr class="process-list" v-for="(task, i) in process" :key="i">
          <td>
            <div class="base-info" :class="{ 'marginBottom': task.signature }">
              <span v-if="!task.isComment">{{ task.userName }} {{ task.result }}</span>
              <div v-if="task.isComment" class="commentDiv">
                <div v-html="task.comment"></div>
                {{ task.title }}
              </div>
              <span>{{ task.finishTime ? getTime(task.finishTime) : '' }}</span>
              <img class="sign" v-if="task.signature" :src="task.signature">
            </div>
          </td>
        </tr>
        <tr v-if="getName">
          <td>抄送：{{ getName }}</td>
        </tr>
        <tr>
          <th>审批结果</th>
          <td>{{ status.text }}</td>
        </tr>
      </table>
    </div>
    <!-- <div @click="handleText">拿值</div> -->
    <div class="footer">
      <div>打印时间：{{ moment().format('yyyy-MM-DD HH:mm:ss') }}</div>
      <div>打印人：{{ loginUser.name }}</div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import DefaultPrintItem from './DefaultPrintFormItem.vue'
import QrcodeVue from "qrcode.vue";

export default {
  name: 'DefaultPrinter',
  components: { QrcodeVue, DefaultPrintItem },
  props: {
    instance: {
      required: true,
    },
    status: {
      required: true,
    },
  },
  computed: {
    loginUser() {
      return this.$wflow.loginUser
    },
    qrCode() {
      return `${window.location.origin}/#/mbInstance?instanceId=${this.instance.instanceId}`
    },
    process() {
      let userNodes = this.instance.progress.filter((p) => p.nodeType === 'APPROVAL' || p.result === 'comment')
      let userTask = []
      userNodes.forEach((un) => {
        if (un.result === 'comment') {
          userTask.push(this.getComment(un))
        } else if (Array.isArray(un.users)) {
          userTask.push(...un.users.map((u) => this.getTask(u)))
        } else {
          userTask.push(this.getTask(un))
        }
      })
      return userTask
    },
    getName() {
      const names = this.instance.progress
        .filter(p => p.nodeType === 'CC')
        .flatMap(item => {
          if (Array.isArray(item.users)) {
            return item.users.map(user => this.getTask(user))
          } else {
            return [this.getTask(item)]
          }
        }).map(c => c.userName)
      return names.join('、')
    },

    formItems() {
      let result = []
      this.getItems(this.instance.formItems, result)
      return result
    },
  },
  data() {
    return {
      moment,
    }
  },
  methods: {
    handleText() {
      console.log(this.instance)
    },
    getTime(time) {
      return moment(time).format('YYYY-MM-DD HH:mm:ss')
    },
    getItems(items, collects) {
      items.forEach((item) => {
        if (item.name === 'SpanLayout') {
          this.getItems(item.props.items, collects)
        } else {
          collects.push(item)
        }
      })
    },
    getTask(item) {
      return {
        userName: item.user.name,
        result: this.getResult(item),
        signature: item.signature,
        finishTime: item.finishTime,
        comment: item.comment && item.comment.length > 0 ? item.comment[0].text : '',
      }
    },
    getComment(item) {
      const text = item.comment && item.comment.length > 0 ? item.comment[0].text : ''
      const attachments = item.comment[0].attachments?.map(i => i.name) || []
      const file = attachments.length > 0 ? `(${attachments.join('、')})` : ''
      return {
        isComment: true,
        title: item.user.name + ' 添加评论',
        signature: item.signature,
        finishTime: item.finishTime,
        comment: text + file,
      }
    },
    getResult(task) {

      if (task.result === 'agree') {
        return '已同意'
      } else if (task.result === 'refuse') {
        return '已拒绝'
      } else if (task.result === 'transfer') {
        return '已转交'
      } else if (task.result === 'recall') {
        return '已退回'
      } else if (task.result === 'pending') {
        return '待处理'
      } else if (!task.result && task.finishTime) {
        return '已取消'
      } else {
        return '审批中'
      }
    },
  },
}
</script>

<style lang="less" scoped>
.print {
  position: relative;
  color: #303233;

  .content {
    position: relative;
  }

  .result {
    position: absolute;
    width: 100px;
    height: 100px;
    right: 0;
    top: -20px;
  }

  .qr-code {
    position: absolute;
    text-align: center;
    top: 0;
    right: 0;
  }
}

h2 {
  text-align: center;
}

.header {
  font-size: 15px;
  margin-bottom: 10px;

  div {
    padding: 2px 0 0;
  }
}

table {
  font-size: 15px;
  width: 100%;
  border-collapse: collapse;
  padding: 2px;

  th {
    width: 30%;
  }
}

table tr th,
table tr td {
  text-align: left;
  border: 1px solid #464648;
  padding: 10px;
  font-weight: normal;
}

.footer {
  font-size: 16px;
  margin-top: 30px;
  display: flex;

  div:nth-child(1) {
    margin-right: 40px;
  }
}

.process-list {
  .base-info {
    position: relative;
    display: flex;
    justify-content: space-between;

    .sign {
      position: absolute;
      left: 80px;
      width: 110px;
      height: 50px;
    }
  }

  .marginBottom {
    margin-bottom: 20px;
  }

  .comment {
    margin-left: 10px;
    margin-top: 40px;
  }
}

.commentDiv {
  flex: 1;
  margin-right: 10px;
}

:deep(p) {
  margin: 0;
  padding: 0;
}
</style>
