<template>
  <div class="progress">
    <div v-for="item in progress" :key="item.taskId" class="progress-item">
      <div>
        <div class="item-user">
          <div class="item-user-avatar">
            <template v-if="item.user">
              <avatar :size="38" :show-name="false" :status="getStatus(item)" :name="item.user.name"
                :src="item.user.avatar" @click="handleAvatar(item.user)" />
            </template>
            <template v-else>
              <span v-if="item.nodeType === 'APPROVAL'" class="iconfont icon-huiyi multi-user-avatar"></span>
              <img src="@/assets/image/cc.svg" class="avatar-cc" v-else>
            </template>
          </div>
          <div :class="{ 'item-user-desc': true, 'subprocess': item.nodeType === 'SUBPROC' }"
            @click="showItemNode(item)">
            <div>
              {{ item.name }}
              <span v-if="item.nodeType === 'SUBPROC'" style="margin-left: 10px">
                <icon name="el-icon-view" /> 查看
              </span>
            </div>
            <div>
              {{ getDesc(item) }}
              <span v-if="item.nodeType === 'CC' && !item.user && status == '审批通过'" class="readSpan"
                @click="handleRead(item, getReadDesc(item))">
                {{ getReadDesc(item) == 0 ? '全部未读' : getReadDesc(item) == item.users.length ? '全部已读' : '已读' +
                  getReadDesc(item) +
                  '人' }}
              </span>
            </div>
          </div>
        </div>
        <div class="time">
          {{ item.finishTimeStr ? getShortTime(item.finishTimeStr) : '' }}
        </div>
      </div>
      <div v-if="item.users && item.users.length > 0" class="multi-user">
        <div v-for="subItem in item.users" :key="subItem.taskId" class="">
          <div class="item-user-avatar" :class="{ 'isRead': true }">
            <avatar showY :size="35"
              :status="item.name == '抄送人' && status == '审批通过' ? getRead(subItem) : subItem.nodeType == 'APPROVAL' ? getStatus(subItem) : ''"
              :name="subItem.user.name" :src="subItem.user.avatar" @click="handleAvatar(subItem.user)" />
          </div>
        </div>
      </div>
      <div v-show="(item.comment && item.comment.length > 0) || item.signature" class="user-comment">
        <div v-if="item.signature" style="display: flex; align-items: center">
          <span>
            <icon name="el-icon-editpen" /> 签字：
          </span>
          <img :src="item.signature" style="width: 140px; height: 60px" />
        </div>
        <div v-for="cmt in item.comment" :key="cmt.id" style="position: relative">
          <div class="user-comment-user" v-if="item.users">
            <avatar :size="30" :name="cmt.user.name" :src="cmt.user.avatar" />
            <span>（添加了评论）</span>
          </div>
          <div class="user-comment-time" v-if="item.users">
            {{ cmt.createTime ? getShortTime(cmt.createTime) : '' }}
          </div>
          <div class="user-comment-content" :class="{ 'user-withdraw': cmt.user.id == $wflow.loginUser.id }">
            <div class="text-comment" v-html="cmt.text"></div>
            <div class="image-comment" v-show="cmt.attachments.length > 0">
              <el-image class="image" preview-teleported :src="img" v-for="(img, i) in filterImages(cmt.attachments)"
                :key="i" :preview-src-list="filterImages(cmt.attachments)" :initialIndex="i" />
            </div>
            <div class="file-comment">
              <!-- <ellipsis class="file-item" type="primary" @click="download(file)" :content="file.name"
                v-for="file in filterFiles(cmt.attachments)" :key="file.id">
                <template #pre>
                  <icon name="el-icon-document" />
                </template>
                <template #aft>
                  <el-button type="text" size="mini" style="margin-left: 10px" @click.stop="downloadUrl(file)">
                    <icon name="el-icon-download" />
                    下载</el-button>
                </template>

              </ellipsis> -->
              <div v-for="file in filterFiles(cmt.attachments)" :key="file.id" class="center-align">
                <ellipsis class="file-item" type="primary" @click="download(file)" :content="file.name">
                  <template #pre>
                    <icon name="el-icon-document" style="top:1px"></icon>
                  </template>
                  <template #aft>
                    <!-- <el-tag size="small">{{ getSize(file.size) }}</el-tag> -->
                  </template>
                </ellipsis>
                <el-button type="text" size="mini" style="margin-left: 10px" @click="downloadUrl(file)"> <icon name="el-icon-download" />下载</el-button>
              </div>  




            </div>
            <el-button type="text" class="withdrawBtn" v-if="cmt.deleteFlag"
              @click="handleWithdraw(cmt.id)">撤回</el-button>
          </div>
        </div>
      </div>
    </div>
    <el-drawer :size="isMobile ? '100%' : '500px'" append-to-body direction="rtl" title="子流程详情" v-model="showSubProc">
      <instance-view :instance-id="subProcInst" />
    </el-drawer>
    <dialogHref v-model="dialogRead" ref='hrefRef'></dialogHref>
  </div>
  <readFile ref="readFileRef" />
  <ccRead ref="ccReadRef" :tenantId="tenantId" />
  <UserDialog ref="userRef"></UserDialog>
</template>

<script>
import { deleteFlag, commentDelete } from '@/api/processTask'
import { getTaskResult } from "@/utils/ProcessUtil.js";
import { defineAsyncComponent } from "vue";
import UserDialog from '@/views/system/orgstructure/userDialog.vue'
import dialogHref from './dialogHref.vue'
import readFile from '@/views/system/notify/list/readFile.vue'
import ccRead from './ccReadList.vue'
import { ElMessageBox } from 'element-plus'
import { useWFlowStore } from '@/store/modules/wflow'
const message = useMessage()
const { t } = useI18n()
export default {
  name: 'ProcessProgress',
  components: {
    InstanceView: defineAsyncComponent(_ => import("./ProcessInstancePreview.vue")),
    UserDialog,
    dialogHref,
    readFile,
    ccRead
  },
  props: {
    progress: {
      type: Array,
      required: true,
      default: () => {
        return []
      },
    },
    result: {
      required: true,
      default: null
    },
    status: {
      required: true,
      default: '未知'
    },
    instanceId: {
      type: String,
      default: ''
    },
    tenantId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showSubProc: false,
      subProcInst: null,
      dialogRead: false,
      isFlagResult: {}
    }
  },
  computed: {
    isMobile() {
      return window.screen.width < 450
    },
    statusIcon() {
      switch (this.result) {
        case 'RUNNING':
          return 'el-icon-morefilled icon-morefilled'
        case 'COMPLETE':
          return 'el-icon-morefilled icon-morefilled'
        case 'PASS':
          return 'el-icon-successfilled icon-success'
        case 'CANCEL':
          return 'el-icon-circleclose icon-circleclose'
        case 'REFUSE':
          return 'el-icon-circleclosefilled icon-error'
      }
    },
  },
  mounted() {
    const myDiv = document.getElementsByClassName('customClass')
    const that = this;
    for (let i = 0; i < myDiv.length; i++) {
      myDiv[i].addEventListener('click', function (event) {
        const dataId = event.target.getAttribute('data-id')
        that.getUser(dataId.replace(/["'\\]/g, ''))
      })
    }
    const aDiv = document.getElementsByClassName('customA')
    for (let i = 0; i < aDiv.length; i++) {
      aDiv[i].addEventListener('click', function (event) {
        const dataId = event.target.getAttribute('data-href')
        that.getLinkDetail(dataId.replace(/["'\\]/g, ''))
      })
    }
    // this.getRes()
  },
  methods: {
    handleAvatar(user) {
      if (user.type != 'dept') {
        this.$refs.userRef.open(user.id, this.tenantId)
      }
    },
    async isFlag(id) {
      try {
        const res = await deleteFlag({ id: id })
        if (res.code === 0 && res.data) {
          return true
        } else {
          return false
        }
      } catch (error) {
        return false
      }
    },
    async getRes() {
      try {
        const promises = []
        this.progress.forEach((item) => {
          if (item.comment && item.comment.length > 0) {
            item.comment.forEach((cmt) => {
              const promise = this.isFlag(cmt.id)
                .then(flag => {
                  // console.log(flag, cmt.id, 'flag')
                  this.isFlagResult[cmt.id] = flag
                })
                .catch(error => {
                  this.isFlagResult[cmt.id] = false
                });
              promises.push(promise)
            })
          }
        })
        await Promise.all(promises)
      } catch (error) {
        console.error('Error checking flags:', error)
      }
    },
    async handleWithdraw(id) {
      ElMessageBox.confirm('是否撤回当前评论？', '系统提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        commentDelete({ id: id }).then((res) => {
          if (res.code == 0) {
            this.$message.success('操作成功')
            this.$emit('success')
          } else {
            this.$message.error(res.msg)
          }
        }).catch((err) => {
          this.$message.error('操作失败')
        })
      })
    },
    getLinkDetail(e) {
      this.dialogRead = true
      this.$refs.hrefRef.getUrl(e)
    },
    getUser(e) {
      this.$refs.userRef.open(e, this.tenantId)
    },
    filterImages(attachments) {
      return (attachments || [])
        .filter((f) => f && f.isImage)
        .map((f) => {
          return this.$getRes(f.url)
        })
    },
    filterFiles(attachments) {
      return (attachments || [])
        .filter((f) => f && !f.isImage)
        .map((f) => {
          // return {...f, url: this.$getRes(f.url)}
          return { ...f, url: f.url }
        })
    },
    download(file) {
      // window.open(`${this.$getRes(file.url)}?name=${file.name}`, '_blank')
      this.$refs.readFileRef.open(file, true, '1')
    },
    showItemNode(item) {
      if (item.nodeType === 'SUBPROC') {
        this.subProcInst = item.taskId
        this.showSubProc = true
      }
    },
    getSize(size) {
      if (size > 1048576) {
        return (size / 1048576).toFixed(1) + 'MB'
      } else if (size > 1024) {
        return (size / 1024).toFixed(1) + 'KB'
      } else {
        return size + 'B'
      }
    },
    getShortTime(time) {
      if (time) {
        return time.substring(5, 16)
      }
      return '审批中'
    },
    getStatus(item) {
      if (item.finishTime === null) {
        return 'pending'
      } else if (item.nodeType === 'CC') {
        return 'cc'
      } else if (item.result === 'agree') {
        return 'success'
      } else if (item.result === 'refuse') {
        return 'error'
      } else if (item.result === 'comment') {
        return 'comment'
      } else if (item.result === 'transfer') {
        return 'transfer'
      } else if (item.result === 'recall') {
        return 'recall'
      } else if (item.nodeType === 'cancel') {
        return 'cancel'
      } else {
        return undefined
      }
    },
    getDesc(item) {
      if (item.nodeType === 'ROOT') {
        return useWFlowStore().loginUser.id == item.user.id ? '我' : item.user.name
      } else if (item.nodeType === 'APPROVAL') {
        if (item.user) {
          if (item.result != 'pending') {
            return `${useWFlowStore().loginUser.id == item.user.id ? '我' : item.user.name}（${getTaskResult(item).text}）`
          } else {
            return `${useWFlowStore().loginUser.id == item.user.id ? '我' : item.user.name}`
          }
        }
        let desc = (item.users || []).length + '人（'
        switch (item.approvalMode) {
          case 'AND':
            return desc + '会签）'
          case 'OR':
            return desc + '或签）'
          case 'NEXT':
            return desc + '顺序会签）'
        }
      } else if (item.nodeType === 'CC') {
        if (item.user) {
          return `抄送${item.user.name}`
        }
        return `抄送${item.users.length}人`
      } else if (item.nodeType === 'SUBPROC') {
        switch (item.result) {
          case 'agree': return `${item.user.name} 发起的子流程（审批通过）`;
          case 'cancel': return `${item.user.name} 发起的子流程（被撤销）`;
          case 'refuse': return `${item.user.name} 发起的子流程（被驳回）`;
          default: return `${item.user.name} 发起的子流程（正在审批中）`;
        }
      } else if (item.result === 'comment') {
        return `${useWFlowStore().loginUser.id == item.user.id ? '我' : item.user.name}（添加了评论）`
      }
    },
    // 获取抄送人为数组时 已读人员数量
    getReadDesc(item) {
      return item.users.filter(child => child.user.read).length
    },
    getRead(item) {
      if (item.user.read) {
        return 'success'
      } else {
        return undefined
      }
    },
    handleRead(item, length) {
      this.$refs.ccReadRef.open(item, length, this.instanceId)
    },
    downloadUrl(file) {
      // console.log("downloadUrl file=",file.downloadUrl)
      // console.log("downloadUrl file=",file.name)
      const link = document.createElement('a');
          fetch(file.downloadUrl)
            .then((res) => res.blob())
            .then((blob) => {
            // 将链接地址字符内容转变成blob地址
              link.href = URL.createObjectURL(blob);
              link.download = file.name;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            });
            
    }  
  },
  emits: ['success'],
}
</script>

<style lang="less" scoped>
.progress {
  position: relative;
}

.subprocess {
  cursor: pointer;
  border-radius: 5px;

  &:hover {
    background: @theme-aside-bgc;
    color: @theme-primary;
  }
}

.progress::after {
  content: '';
  position: absolute;
  width: 3px;
  top: 0;
  left: 18px;
  color: #dce3eb;
  background: #dce3eb;
  height: 100%;
}

.end-process {
  height: 40px;

  .node-icon {
    span {
      font-size: 30px;
    }

    .icon {
      left: 0 !important;
      top: 0 !important;
    }

    .icon-success {
      color: @theme-success !important;
      background: white !important;
    }

    .icon-error {
      color: @theme-danger !important;
      background: white !important;
    }

    .icon-circleclose {
      color: #979797 !important;
      background: white !important;
    }

    .icon-morefilled {
      color: #b0b0b0 !important;
      background: #e4e4e4 !important;
    }
  }
}

.progress-item {
  z-index: 1;
  margin-bottom: 30px;
  position: relative;

  .item-user {
    &>div {
      display: inline-block;
    }
  }

  .user-comment {
    margin-left: 60px;
    font-size: 14px;
    position: relative;
    padding-top: 5px;
    word-wrap: break-word;

    .user-comment-user {
      display: flex;
      align-items: center;
    }

    :deep(.a-img span) {
      font-size: 12px;
    }

    .user-comment-time {
      position: absolute;
      right: 8px;
      top: 7px;
      color: #8c8c8c;
      font-size: small;
    }

    .user-comment-content {
      margin: 0 0 6px;
      background: #f3f4f7;
      padding: 12px;
      border-radius: 3px;
      position: relative;


      :deep(.image-comment) {
        padding: 5px 0;

        .image {
          height: 60px;
          width: 60px;
          padding: 5px;
        }

        img {
          border-radius: 5px;
        }
      }

      .file-comment {
        .file-item {
          display: block;
          margin-bottom: 5px;
          color: @theme-primary;
          cursor: pointer;
        }
      }

      .withdrawBtn {
        position: absolute;
        top: 50%;
        right: 12px;
        transform: translate(0%, -50%);
      }
    }

    .user-withdraw {
      padding-right: 50px;
    }
  }

  .item-user-avatar {
    background: white;
    position: relative;
    padding: 4px 0;

    :deep(.icon) {
      // border-radius: 50%;
      // font-size: 15px;
      // color: white;
      // border: 2px solid white;
      // position: absolute;
      // padding: 0px;
      right: -6px;
      top: 23px;
    }

    .el-icon-promotion {
      padding: 1px;
      font-size: 12px;
    }

    .el-icon-more {
      color: white;
      font-size: 6px !important;
    }

    .multi-user-avatar {
      display: flex;
      height: 38px;
      width: 38px;
      background: @theme-primary;
      color: white;
      font-size: 28px;
      border-radius: 6px;
      justify-content: center;
      align-items: center;
      right: -43px;
      top: -5px;
    }

    .avatar-cc {
      height: 38px;
      width: 38px;
      // margin-bottom: -25px;
      // border-bottom: 4px solid #fff;
      border-radius: 6px;
      background: #1989fa;
    }
  }

  .item-user-desc {
    position: absolute;
    left: 60px;
    top: 3px;
    font-size: 14px;

    div:nth-child(1) {
      color: #171a1d;
    }

    div:nth-child(2) {
      font-size: 13px;
      color: #a2a3a5;
      margin-top: 2px;
    }
  }
}

.time {
  font-size: 14px;
  color: #a2a3a5;
  position: absolute;
  right: 0;
  top: 4px;
}

.multi-user {
  margin-top: 5px;
  display: grid;
  margin-left: 50px;
  grid-template-columns: repeat(7, 60px);
  /*.item-user-avatar {
    :deep(.icon) {
      right: 2px;
    }
  }*/
}

:deep(.text-comment>p) {
  margin: 0 !important;
}

.readSpan {
  margin-left: 10px;
  cursor: pointer;
  color: #1989fa;
}

:deep(.isRead) {
  .icon {
    top: -2px !important;
    border-width: 1px;
    font-size: 14px;
    height: fit-content;
  }
}

.avatar {
  cursor: pointer;
}
</style>
