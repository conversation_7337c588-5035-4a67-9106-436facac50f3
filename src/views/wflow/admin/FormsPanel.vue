<template>
  <div class="from-panel" ref="panel" v-loading="loading">
    <div class="from-title">
      <div>
        <span>📢 长按流程可拖拽排序，拖拽分组名右侧
          <icon name="el-icon-rank" /> 可对分组排序
        </span>
      </div>
      <div>
        <el-input v-model="modelName" class="!w-240px mr-12px" placeholder="搜索流程名称" clearable
          @change="getGroupModels"></el-input>
        <el-button type="primary" icon="el-icon-plus" @click="newProcess(null)">新建表单</el-button>
        <el-button icon="el-icon-plus" @click="addGroup">新建分组</el-button>
        <el-button type="primary" icon="el-icon-switchbutton" :disabled="selectedIds.length == 0"
          @click="handleBatchStop">批量停用</el-button>
      </div>
    </div>
    <draggables v-model="groups" item-key="id" group="group" handle=".el-icon-rank" filter=".undrag"
      :component-data="{ tag: 'div', type: 'transition-group' }" @start="groupsSort = true" v-bind="groupDragOptions"
      @end="doGroupSort" class="dragDiv">
      <template #item="{ element }">
        <div :class="{ 'form-group': true, 'undrag': (element.id === 0 || element.id === undefined) }"
          v-show="element.id > 1 || element.items.length > 0">
          <div class="form-group-title">
            <span>{{ element.name }}</span>
            <span>({{ element.items.length }})</span>
            <icon name="el-icon-rank" title="长按拖动可对分组排序"></icon>
            <div v-if="element.name != '已停用' && element.name != '其它'">
              <el-button type="primary" link icon="el-icon-edit" size="small" @click="editGroup(element)">修改</el-button>
              <el-button type="primary" link icon="el-icon-delete" size="small"
                @click="delGroup(element)">删除</el-button>
              <el-button type="primary" link icon="el-icon-documentcopy" size="small"
                @click="handleCopy(element.id)">复制流程</el-button>
            </div>
          </div>
          <group-forms :group="element" :group-sort="groupsSort" :groups="groups" :sort="true"
            :form-drag-options="formDragOptions" v-model="element.items" @refresh="getGroupModels"
            @itemSelect="handleItemSelect" />
          <div style="text-align: center" v-if="element.items === undefined || element.items.length === 0">
            <el-button style="padding-top: 0" link icon="el-icon-plus" @click="newProcess(element.id)">创建新表单</el-button>
          </div>
        </div>
      </template>
    </draggables>


    <el-dialog v-model="formTypeVisible" title="创建表单类型" width="500">
      <div class="center-view" style="padding: 50px 0 50px 0; flex-flow: column">
        <div>
          <el-radio-group v-model="formTypeForm.formType">
            <div>
              <div>
                <el-radio :value="1" size="large">
                  <div style="color:#666;">
                    审批流程表
                  </div>
                  <div style="color:#999;">
                    常规审批流程，无特殊要求
                  </div>
                </el-radio>
              </div>
              <div style="margin-top:20px;">
                <el-radio :value="2" size="large">
                  <div style="color:#666;">
                    工单流程表
                  </div>
                  <div style="color:#999;">
                    事项协调工单流程，对审核时间和工单交付有严格要求
                  </div>
                </el-radio>
              </div>
            </div>
          </el-radio-group>
        </div>
      </div>

      <template #footer> 
        <el-button @click="formTypeVisible = false">取 消</el-button>
      <el-button type="primary" @click="newProcess2(null)">确 定</el-button>
     
    </template>
    </el-dialog>


  </div>
</template>

<script>
import draggables from 'vuedraggable'
import modelGroupApi from '@/api/modelGroup'
import GroupForms from "./GroupForms.vue";
export default {
  name: 'FormsPanel',
  components: { GroupForms, draggables },
  data() {
    return {
      visible: false,
      loading: false,
      groupsSort: false,
      groups: [],
      selectedIds: [],
      groupDragOptions: {
        animation: 300,
        group: "form",
        disabled: false,
        sort: true,
        scroll: true,
        ghostClass: "choose",
      },
      formDragOptions: {
        animation: 300,
        delay: 200,
        chosenClass: 'choose',
        scroll: true,
        sort: true,
      },
      modelName: '',
      formTypeVisible: false,
      formTypeForm: {
        formType: 1,
      }
    }
  },
  mounted() {
    this.getGroupModels()
  },
  methods: {
    getGroupModels() {
      this.loading = true
      modelGroupApi.getGroupModels({ modelName: this.modelName }).then((rsp) => {
        this.loading = false
        this.groups = rsp.data
        this.groups.forEach((group) => {
          group.items.forEach((item) => {
            item.logo = JSON.parse(item.logo)
            item.isSel = false;
          })
        })
        this.selectedIds = [];
      }).catch((err) => {
        this.loading = false
        this.$err(err, '获取分组异常')
      })
    },
    newProcess(groupId) {
      // this.$store.commit('setIsEdit', false)
      this.$wflow.isEdit = false

      this.formTypeForm.formType=1 
      this.formTypeVisible = true


      return
      // window.open(`/#/workspace/design${
      //     this.$isNotEmpty(groupId) ? '?groupId=' + groupId : ''
      //   }`, '_blank')
      // this.$router.push(`/workspace/design?randomNum=${this.getRandom()}${this.$isNotEmpty(groupId) ? '&groupId=' + groupId : ''}`)
    },
    newProcess2(groupId){
      this.formTypeVisible = false
      this.$router.push(`/workspace/design?randomNum=${this.getRandom()}${this.$isNotEmpty(groupId) ? '&groupId=' + groupId : ''}${this.formTypeForm.formType ? '&formType=' + this.formTypeForm.formType : ''}`)
    },
    getRandom() {
      return ('num' + (Math.floor(Math.random() * (99999 - 10000)) + 10000).toString() + new Date().getTime().toString().substring(5))
    },
    doGroupSort(group) {
      this.groupsSort = false
      this.loading = true
      modelGroupApi.modelGroupsSort(this.groups.map((g) => g.id)).then((rsp) => {
        this.loading = false
        this.$ok(rsp, '分组排序成功')
        this.getGroupModels()
      }).catch((err) => {
        this.loading = false
        this.getGroupModels()
        this.$err(err, '分组排序失败')
      })
    },
    addGroup() {
      this.$prompt('请输入要添加的组名', '新的分组名', {
        confirmButtonText: '提交',
        cancelButtonText: '取消',
        inputPattern: /^[\u4E00-\u9FA5A-Za-z0-9\\-]{1,30}$/,
        inputErrorMessage: '分组名不能为空且长度小于30',
        inputPlaceholder: '请输入分组名',
      }).then(({ value }) => {
        modelGroupApi.createModelGroup({ name: value }).then((rsp) => {
          this.$ok(rsp, '添加分组成功')
          this.getGroupModels()
        }).catch((err) => this.$err(err, '添加分组失败'))
      })
    },
    delGroup(group) {
      this.$confirm('删除分组并不会删除表单，表单将会被转移到 “其他” 分组，确定要删除分组 ' + group.name + '?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
      ).then(() => {
        this.loading = true
        modelGroupApi.deleteModelGroup(group.id).then((rsp) => {
          this.loading = false
          this.$ok(rsp, '删除分组成功')
          this.getGroupModels()
        }).catch((err) => {
          this.loading = false
          this.$err(err, '删除分组失败')
        })
      })
    },
    editGroup(group) {
      this.$prompt('请输入新的组名', '修改分组名', {
        confirmButtonText: '提交',
        cancelButtonText: '取消',
        inputPattern: /^[\u4E00-\u9FA5A-Za-z0-9\\-]{1,30}$/,
        inputErrorMessage: '分组名不能为空且长度小于30',
        inputPlaceholder: '请输入分组名',
        inputValue: group.name,
      }).then(({ value }) => {
        this.loading = true
        modelGroupApi.updateModelGroupName(group.id, { name: value }).then((rsp) => {
          this.loading = false
          this.$ok(rsp, '修改成功')
          this.getGroupModels()
        }).catch((err) => {
          this.loading = false
          this.$err(err, '修改失败')
        })
      })
    },
    // 复制流程
    handleCopy(e) {
      this.$prompt('请输入要复制的流程编号', '复制流程', {
        confirmButtonText: '提交',
        cancelButtonText: '取消',
        inputPattern: /^[\u4E00-\u9FA5A-Za-z0-9\\-]{1,300}$/,
        inputErrorMessage: '请输入流程编号',
        inputPlaceholder: '请输入流程编号',
      }).then(({ value }) => {
        modelGroupApi.modelCopy({
          groupId: e,
          id: value
        }).then((rsp) => {
          ElMessage.success(value + "复制成功")
          this.getGroupModels()
        }).catch((err) => ElMessage.error(err))
      })
    },
    handleItemSelect(isSel, formId) {
      if (isSel) {
        this.selectedIds.push(formId)
      } else {
        const index = this.selectedIds.indexOf(formId)
        if (index !== -1) {
          this.selectedIds.splice(index, 1)
        }
      }
    },
    handleBatchStop() {
      this.$confirm('确定要停用这些表单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
      ).then(() => {
        this.loading = true
        modelGroupApi.batchEnOrDisModel(true, this.selectedIds).then((rsp) => {
          this.loading = false
          this.$ok(rsp, '停用成功')
          this.getGroupModels()
        }).catch((err) => {
          this.loading = false
          this.$err(err, '停用失败')
        })
      })
    },
  },
}
</script>

<style lang="less" scoped>
body {
  background: #ffffff !important;
}

.undrag {
  background: #ebecee !important;
}

.from-panel {
  padding: 20px;
  background: #fff;
  // position: relative;

  :deep(.from-title) {
    position: absolute;
    right: 15px;
    left: 15px;
    /* height: 50px; */
    z-index: 10;
    background: #fff;
    top: 0;
    padding: 15px;
    border-top: 15px solid #F3F4F7;

    &>div:first-child {
      font-size: 13px;
      color: @theme-warning;
      margin-bottom: 10px;
    }

    &>div:last-child {
      text-align: left;
      margin-top: 15px;
    }
  }

  //height: 100vh;
}

.choose {
  cursor: move;
  border: 1px dashed @theme-primary !important;
}

.form-group {
  margin-bottom: 20px;
  padding: 0 0 15px 0;
  border-radius: 10px;
  background: @theme-aside-bgc;
  border: 1px solid @theme-aside-bgc;

  .form-group-title {
    padding: 5px 20px;
    height: 40px;
    line-height: 40px;

    .el-icon-rank {
      margin-left: 10px;
      display: none;
      cursor: move;
    }

    &:hover {
      .el-icon-rank {
        display: inline-block;
      }
    }

    &>span:nth-child(2) {
      margin-left: 5px;
      color: #8c8c8c;
      font-size: smaller;
    }

    &>div {
      float: right;

      button {
        color: #8c8c8c;
      }
    }
  }
}

@media only screen and (max-width: 1000px) {
  .desp {
    display: none !important;
  }
}

@media only screen and (max-width: 800px) {
  .from-panel {
    padding: 50px 10px;
  }
}

@media(max-width:768px) {
  .from-panel {

    :deep(.from-title) {
      &>div:last-child {
        text-align: left;

        .el-input {
          display: block;
          margin-bottom: 10px;
        }
      }
    }

    //height: 100vh;
  }
}

.dragDiv {
  margin-top: 80px;
}

.dragSpan {
  margin-bottom: 0 !important;
}
</style>
