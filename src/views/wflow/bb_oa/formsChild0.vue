<template>
  <div>
    <el-row :gutter="15" :class="{ 'displayRow': nextSub }">
      <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
        <el-card class="marginBottom oaCard" >
          <span class="cardTitle">审批中心</span>
          <span class="cursorPointer" @click="toDetail('0')">详情</span>
          <el-row type="flex" :gutter="30">
            <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24" class="col-my col-my2" @click="toDetail('0')">
              <span class="numTip-c numTip-c-num">{{ taskCount.todo }}</span>
              <span class="numTip-t">待处理</span>
            </el-col>
            <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24" class="col-my" @click="toDetail('1')">
              <span class="numTip-c">
                <Icon icon="ep:document-checked" :size="40" />
              </span>
              <span class="numTip-t">已处理</span>
            </el-col>
            <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24" class="col-my" @click="toDetail('2')">
              <span class="numTip-c">
                <Icon icon="ep:document-add" :size="40" />
              </span>
              <span class="numTip-t">已发起</span>
            </el-col>
            <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24" class="col-my" @click="toDetail('3')">
              <span class="numTip-c">
                <Icon icon="ep:document" :size="40" />
              </span>
              <span class="numTip-t">我收到的</span>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
      <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
        <el-card class="oaCard" style="min-height:175px">
          <span class="cardTitle">场景</span>
        </el-card>
      </el-col>
    </el-row>
    <detail :type="'forms'" :isOA="true"></detail>
  </div>
</template>

<script type="ts" setup>
import { Icon } from '@/components/Icon'
import { getProcessCountData } from '@/api/modelGroup'
import detail from '@/views/wflow/workspace/oa/fromsApp_new.vue'
import { useMyStore } from '@/store/modules/jump'
const loading = ref(true)
const taskCount = ref({})
const getTotal = async () => {
  loading.value = true
  try {
    const res = await getProcessCountData()
    taskCount.value = res.data
  } finally {
    loading.value = false
  }
}

const nextSub = ref(false)
const store = useMyStore()
watch(() => store.launchStatus, (newValue, oldValue) => {
  console.log('Value changed:', newValue, 'from', oldValue);
  nextSub.value = newValue
})





const emit = defineEmits(['getType'])
const toDetail = (e) => {
  emit('getType', e);
}

const getAllApi = async () => {
  await Promise.all([
    getTotal()
  ])
  loading.value = false
}
getAllApi()
</script>

<style lang="less" scoped>
.marginBottom {
  position: relative;
}

.cursorPointer {
  position: absolute;
  right: 20px;
  top: 20px;
  font-size: 12px;
}

.col-my {
  text-align: center;
  cursor: pointer;
}

.col-my2 {
  position: relative;
}

.col-my2::after {
  position: absolute;
  content: '';
  right: 0;
  top: 17px;
  width: 1px;
  height: 68%;
  background: #eceded;
  align-items: center;

}

.numTip-c {
  display: block;
  margin: 15px 0 5px;
}

.numTip-t {
  display: block;
  font-size: 14px;
  margin-bottom: 15px;
}

.numTip-c-num {
  color: #3370FF;
  font-weight: bold;
  font-size: 40px;
}
.displayRow{
  display: none;
}
</style>