<template>
  <el-form label-width="90px" require-asterisk-position="" label-position="top" class="rightForm">
    <template v-if="true">
      <el-form-item label="表单ID">
        <el-input size="default" disabled v-model="form.id" />
      </el-form-item>
      <el-form-item label="表单名称">
        <el-input size="default" disabled v-model="form.title" />
      </el-form-item>
    </template>
    <component :is="form.itemType" v-model="form.child" />
  </el-form>
</template>

<script>
import leaveType from './configGroup/leaveType.vue'
import bizNode from './configGroup/bizNode.vue'
import overTime from './configGroup/overTime.vue'
import goOut from './configGroup/goOut.vue'
import businessTrip from './configGroup/businessTrip.vue'
import shiftChange from './configGroup/shiftChange.vue'
import regularEmp from './configGroup/regularEmp.vue'
import resignation from './configGroup/resignation.vue'
import resignationHandover from './configGroup/resignationHandover.vue'
import handover from './configGroup/handover.vue'
import changePost from './configGroup/changePost.vue'
import entry from './configGroup/entry.vue'
import payBatch from './configGroup/payBatch.vue'
import promotion from './configGroup/promotion.vue'
import comprehensive from './configGroup/comprehensive.vue'
import ticketDeduction from './configGroup/ticketDeduction.vue'
export default {
  name: 'FormComponentConfigGroup',
  components: {
    leaveType,
    bizNode,
    overTime,
    goOut,
    businessTrip,
    shiftChange,
    regularEmp,
    resignation,
    resignationHandover,
    handover,
    changePost,
    entry,
    payBatch,
    promotion,
    comprehensive,
    ticketDeduction
  },
  props: {},
  computed: {
    form() {
      return this.$wflow.selectFormItem
    },
  },
  data() {
    return {}
  },
  methods: {},
}
</script>

<style lang="less" scoped>
.rightForm {
  :deep(.el-form-item__label) {
    margin-bottom: 4px !important;
  }
}
</style>
