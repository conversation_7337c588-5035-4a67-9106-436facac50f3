<template>
  <!-- 占位 -->
  <div>
    <div v-if="mode === 'DESIGN'">
     
     
      
    </div>
    <div v-else-if="mode === 'PC' && !readonly">
    
    
      
    </div>
    <div v-else-if="mode === 'MOBILE' && !readonly">
    
      
    
      
    </div>
    <div v-else>
      
    </div>
  </div>
</template>

<script>
import { Field, Popup, Picker, Radio, RadioGroup } from 'vant'
import componentMinxins from '../ComponentMinxins'

export default {
  mixins: [componentMinxins],
  name: 'Blank',
  components: { Radio, RadioGroup, Picker, Field, Popup },
  props: {
    modelValue: {
      type: String,
      default: null,
    },
    placeholder: {
      type: String,
      default: '请选择选项',
    },
    options: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      showPicker: false,
      // _value:'否',
    }
  },
  methods: {
    onConfirm(val) {
      this.showPicker = false
      this._value = val
    },
  },
  emits: ['update:modelValue'],
}
</script>

<style scoped>

.placeholder {
  margin-left: 10px;
  color: #adabab;
  font-size: smaller;
}
</style>
