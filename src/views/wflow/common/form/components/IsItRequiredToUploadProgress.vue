<template>
  <div>
    <div v-if="mode === 'DESIGN'">
     
      <el-radio-group v-model="_value" >
        <el-radio class="w-row-text" style="margin: 5px"  disabled  v-for="(op, index) in options" :key="index" :label="op">{{ op }}</el-radio>
      </el-radio-group>
      <div style="margin-bottom: 5px;">上传周期</div>
      <div>
        <el-radio class="w-row-text" style="margin: 5px"  disabled  v-for="(op, index) in options2" :key="index" :label="op">{{ op }}</el-radio>
      </div>
      <div style="padding-left:5px;">
        <el-checkbox-group  v-model="_value">
          <el-checkbox class="w-row-text" disabled v-for="(op, index) in options3" :key="index" :label="op">{{ op }}</el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
    <div v-else-if="mode === 'PC' && !readonly">
    
      
      <el-radio-group v-model="_value" >
        <el-radio class="w-row-text" style="margin: 5px" v-for="(op, index) in options" :key="index" :label="op">{{ op }}</el-radio>
      </el-radio-group>
    </div>
    <div v-else-if="mode === 'MOBILE' && !readonly">
    
      
      <radio-group v-model="_value" direction="horizontal">
        <radio style="margin: 5px" v-for="(op, index) in options" :key="index" :name="op">{{ op }}</radio>
      </radio-group>
    </div>
    <div v-else>
      {{ _value }}
    </div>
  </div>
</template>

<script>
import { Field, Popup, Picker, Radio, RadioGroup } from 'vant'
import componentMinxins from '../ComponentMinxins'

export default {
  mixins: [componentMinxins],
  name: 'IsItRequiredToUploadProgress',
  components: { Radio, RadioGroup, Picker, Field, Popup },
  props: {
    modelValue: {
      type: String,
      default: null,
    },
    placeholder: {
      type: String,
      default: '请选择选项',
    },
    options: {
      type: Array,
      default: () => {
        return []
      },
    },
    options2: {
      type: Array,
      default: () => {
        return []
      },
    },
    options3: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      showPicker: false,
      // _value:'否',
    }
  },
  methods: {
    onConfirm(val) {
      this.showPicker = false
      this._value = val
    },
  },
  emits: ['update:modelValue'],
}
</script>

<style scoped>

.placeholder {
  margin-left: 10px;
  color: #adabab;
  font-size: smaller;
}
</style>
