<template>
  <div>
    <el-form label-position="top">
      <el-form-item label="包含类型" class="switchItem">
        <el-switch
          v-model="modelValue[0].props.isShow"
          active-color="#C0CCDA"
          inactive-color="#409EFF"
        ></el-switch>
      </el-form-item>
      <el-form-item label="加班类型选项设置" class="options" v-if="!modelValue[0].props.isShow">
        <template #label>
          <div class="option-item-label">
            <span>加班类型设置（鼠标拖拽排序）</span>
            <el-button
              icon="el-icon-plus"
              link
              type="primary"
              @click="modelValue[0].props.options.push({ name: '', value: '' })"
              >新增选项
            </el-button>
          </div>
        </template>
        <draggables
          item-key="id"
          v-model="modelValue[0].props.options"
          class="option-items"
          :component-data="{ tag: 'div', type: 'transition-group' }"
          handler=".el-icon-rank"
          v-bind="dragOption"
        >
          <template #item="{ element, index }">
            <div class="option-item">
              <el-input
                v-model="element.value"
                style="width: 100px"
                placeholder="选项value值"
                clearable
              />
              <span class="splitSpan">~</span>
              <el-input v-model="element.name" placeholder="选项名称" style="width: 140px" />
              <icon
                name="el-icon-delete del-btn"
                @click="modelValue[0].props.options.splice(index, 1)"
              ></icon>
            </div>
          </template>
        </draggables>
      </el-form-item>
      <el-form-item
        label="加班类型多选模式"
        class="other-switchItem"
        v-if="!modelValue[0].props.isShow"
      >
        <el-switch v-model="modelValue[0].props.multiple"></el-switch>
      </el-form-item>
    </el-form>
    <p>加班时间将自动同步至考勤报表（若当日未排班， 员工可手动修改时长）</p>
  </div>
</template>

<script>
import draggables from 'vuedraggable'

export default {
  components: { draggables },
  props: {
    modelValue: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dragOption: {
        animation: 300,
        sort: true,
        group: 'option'
      }
    }
  },
  methods: {},
  emits: ['update:modelValue']
}
</script>

<style lang="less" scoped>
:deep(.options) {
  .el-form-item__label {
    display: block;
    width: 100%;
    text-align: left;
    padding: 0;
  }

  .el-icon-rank {
    padding-right: 5px;
    cursor: move;
  }
}

.option-div {
  width: 100%;
}

.option-item {
  display: flex;
  align-items: center;
  padding-bottom: 5px;

  :deep(.el-input) {
    // width: 250px;
    float: right;
  }
}

.option-item-label {
  display: flex;
  justify-content: space-between;

  button {
    float: right;
  }
}

.switchItem {
  ::v-deep(.el-switch.is-checked .el-switch__core .el-switch__action) {
    left: 1px !important;
  }

  ::v-deep(.el-switch__core .el-switch__action) {
    left: calc(100% - 17px) !important;
  }
}

.other-switchItem {
  ::v-deep(.el-switch.is-checked .el-switch__core .el-switch__action) {
    left: calc(100% - 17px) !important;
  }

  ::v-deep(.el-switch__core .el-switch__action) {
    left: 1px !important;
  }
}

.splitSpan {
  margin: 0 4px;
}

.del-btn {
  cursor: pointer;
  margin-left: 2px;
  padding: 5px;
  border-radius: 50%;

  &:hover {
    background: #dddfe5;
  }
}
</style>
