<template>
  <div>
    <el-form label-position="top">
      <el-form-item label="出差天数" class="switchItem">
        <span class="spanClass">差旅开始第一天至最后一天的总天数（自然日）</span>
        <el-switch
          v-model="modelValue[8].props.isShow"
          active-color="#C0CCDA"
          inactive-color="#409EFF"
        ></el-switch>
      </el-form-item>
      <el-form-item label="外部人员" class="switchItem">
        <span class="spanClass">员工可以填写同行的外部人员</span>
        <el-switch
          v-model="modelValue[11].props.isShow"
          active-color="#C0CCDA"
          inactive-color="#409EFF"
        ></el-switch>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  props: {
    modelValue: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  methods: {},
  emits: ['update:modelValue']
}
</script>

<style lang="less" scoped>
.spanClass {
  display: block;
  line-height: 1;
  margin: 5px 0;
  color: rgba(25, 31, 37, 0.4);
}

:deep(.el-form-item__content) {
  display: inline;
}

.switchItem {
  margin-bottom: 15px;

  ::v-deep(.el-switch.is-checked .el-switch__core .el-switch__action) {
    left: 1px !important;
  }

  ::v-deep(.el-switch__core .el-switch__action) {
    left: calc(100% - 17px) !important;
  }
}
</style>
