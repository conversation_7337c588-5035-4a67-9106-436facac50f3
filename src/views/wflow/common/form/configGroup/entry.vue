<template>
  <div>
    <p>审批通过后且开通智能人事微应用，员工将自动进入待入职名单。</p>
    <el-form label-position="top">
      <el-form-item label="用人部门设置" class="options">
        <template #label>
          <div class="option-item-label">
            <span>用人部门设置（鼠标拖拽排序）</span>
            <el-button
              icon="el-icon-plus"
              link
              type="primary"
              @click="modelValue[2].props.options.push({ name: '', value: '' })"
              >新增选项
            </el-button>
          </div>
        </template>
        <draggables
          item-key="id"
          v-model="modelValue[2].props.options"
          class="option-items"
          :component-data="{ tag: 'div', type: 'transition-group' }"
          handler=".el-icon-rank"
          v-bind="dragOption"
        >
          <template #item="{ element, index }">
            <div class="option-item">
              <el-input
                v-model="element.value"
                style="width: 100px"
                placeholder="选项value值"
                clearable
              />
              <span class="splitSpan">~</span>
              <el-input v-model="element.name" placeholder="选项名称" style="width: 140px" />
              <icon
                name="el-icon-delete del-btn"
                @click="modelValue[2].props.options.splice(index, 1)"
              ></icon>
            </div>
          </template>
        </draggables>
      </el-form-item>
      <el-form-item label="用人部门多选模式" class="other-switchIte">
        <el-switch v-model="modelValue[2].props.multiple"></el-switch>
      </el-form-item>

      <el-form-item label="员工类型设置" class="options">
        <template #label>
          <div class="option-item-label">
            <span>员工类型设置（鼠标拖拽排序）</span>
            <el-button
              icon="el-icon-plus"
              link
              type="primary"
              @click="modelValue[4].props.options.push({ name: '', value: '' })"
              >新增选项
            </el-button>
          </div>
        </template>
        <draggables
          item-key="id"
          v-model="modelValue[4].props.options"
          class="option-items"
          :component-data="{ tag: 'div', type: 'transition-group' }"
          handler=".el-icon-rank"
          v-bind="dragOption"
        >
          <template #item="{ element, index }">
            <div class="option-item">
              <el-input
                v-model="element.value"
                style="width: 100px"
                placeholder="选项value值"
                clearable
              />
              <span class="splitSpan">~</span>
              <el-input v-model="element.name" placeholder="选项名称" style="width: 140px" />
              <icon
                name="el-icon-delete del-btn"
                @click="modelValue[4].props.options.splice(index, 1)"
              ></icon>
            </div>
          </template>
        </draggables>
      </el-form-item>
      <el-form-item label="员工类型多选模式" class="other-switchIte">
        <el-switch v-model="modelValue[4].props.multiple"></el-switch>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import draggables from 'vuedraggable'
export default {
  components: { draggables },
  props: {
    modelValue: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dragOption: {
        animation: 300,
        sort: true,
        group: 'option'
      }
    }
  },
  methods: {},
  emits: ['update:modelValue']
}
</script>

<style lang="less" scoped>
.spanClass {
  line-height: 1;
  margin: 5px 0;
  color: rgba(25, 31, 37, 0.4);
}
:deep(.options) {
  .el-form-item__label {
    display: block;
    width: 100%;
    text-align: left;
    padding: 0;
  }

  .el-icon-rank {
    padding-right: 5px;
    cursor: move;
  }
}

.option-div {
  width: 100%;
}

.option-item {
  display: flex;
  align-items: center;
  padding-bottom: 5px;

  :deep(.el-input) {
    // width: 250px;
    float: right;
  }
}

.option-item-label {
  display: flex;
  justify-content: space-between;

  button {
    float: right;
  }
}

.switchItem {
  ::v-deep(.el-switch.is-checked .el-switch__core .el-switch__action) {
    left: 1px !important;
  }

  ::v-deep(.el-switch__core .el-switch__action) {
    left: calc(100% - 17px) !important;
  }
}

.other-switchItem {
  ::v-deep(.el-switch.is-checked .el-switch__core .el-switch__action) {
    left: calc(100% - 17px) !important;
  }

  ::v-deep (.el-switch__core .el-switch__action) {
    left: 1px !important;
  }
}

.splitSpan {
  margin: 0 4px;
}

.del-btn {
  cursor: pointer;
  margin-left: 2px;
  padding: 5px;
  border-radius: 50%;

  &:hover {
    background: #dddfe5;
  }
}
</style>
