<template>
  <div class="contentDiv" :class="{ 'dialog-mode': isInDialog }" ref="scrollTarget">
    <div v-for="(item, index) in detailList" :key="index">
      <!-- msg-DIV -->
      <div
        class="msgDiv"
        :class="{
          msgPositionRight: useWFlowStore().loginUser.id == item.senderInfo.userId,
          justifyContentDiv:
            item.messageData.payload.type == 80 ||
            item.messageData.payload.type == 104 ||
            item.messageData.payload.type == 105 ||
            item.messageData.payload.type == 106 ||
            item.messageData.payload.type == 107 ||
            item.messageData.payload.type == 108 ||
            item.messageData.payload.type == 109 ||
            item.messageData.payload.type == 110 ||
            item.messageData.payload.type == 111
        }"
      >
        <!-- 左侧头像 -->
        <div
          v-if="
            item.messageData.payload.type != 80 &&
            item.messageData.payload.type != 104 &&
            item.messageData.payload.type != 105 &&
            item.messageData.payload.type != 106 &&
            item.messageData.payload.type != 107 &&
            item.messageData.payload.type != 108 &&
            item.messageData.payload.type != 109 &&
            item.messageData.payload.type != 110 &&
            item.messageData.payload.type != 111
          "
          @click="handleAvatar(item.senderInfo)"
          :class="{ avatarBox: item.senderInfo.type == 0 }"
        >
          <div class="div1-avatar" v-if="item.senderInfo.portrait">
            <el-avatar shape="square" :size="36" :src="item.senderInfo.portrait" />
          </div>
          <div class="div1" v-else>
            {{
              item.senderInfo.displayName && item.senderInfo.displayName.length > 2
                ? item.senderInfo.displayName.substr(-2)
                : item.senderInfo.displayName
            }}
          </div>
        </div>
        <!-- 信息div -->
        <div
          :class="{ rightColor: useWFlowStore().loginUser.id == item.senderInfo.userId }"
          class="right-msg"
          @contextmenu="showMsgMenu"
          :data-obj="JSON.stringify(item)"
          :data-index="index"
        >
          <!-- 多选模式下的checkbox -->
          <el-checkbox
            v-if="isMultiSelectMsg && [1, 3, 11].includes(item.messageData.payload.type)"
            :model-value="
              selectedMsg.some((msg) => msg.messageData.messageId === item.messageData.messageId)
            "
            @change="(val) => handleSelectMsg(val, item)"
            class="msg-checkbox"
          ></el-checkbox>
          <!-- 姓名 时间 -->
          <div class="tip tipSpecial">
            <span
              class="span1"
              v-if="
                (rowInfo.targetType == 2 &&
                item.messageData.payload.type != 80 &&
                item.messageData.payload.type != 104 &&
                item.messageData.payload.type != 105 &&
                item.messageData.payload.type != 106 &&
                item.messageData.payload.type != 107 &&
                item.messageData.payload.type != 108 &&
                item.messageData.payload.type != 109 &&
                item.messageData.payload.type != 110 &&
                item.messageData.payload.type != 111) ||
                isInDialog
              "
            >
              {{ item.senderInfo.displayName }}</span
            >
            <span>{{ toTime(item.messageData.timestamp) }}</span>
          </div>
          <!-- 文本消息内容1 -->
          <div v-if="item.messageData.payload.type == 1" class="msgText msgMenu">
            <div
              @click="
                textClick(
                  item.messageData.payload.searchableContent,
                  item.messageData.payload.base64edData
                )
              "
              :style="{
                color: isLink(item.messageData.payload.searchableContent) ? '#007AFF' : '',
                cursor: isLink(item.messageData.payload.searchableContent) ? 'pointer' : 'default'
              }"
              v-html="item.messageData.payload.searchableContent"
            >
            </div>
          </div>

          <!-- 语音消息2-->
          <div v-if="item.messageData.payload.type == 2" class="msgText tipText msgMenu">
            <p>语音（请至客户端查看）</p>
          </div>
          <!-- 图片3-->
          <div v-if="item.messageData.payload.type == 3" class="msgImg msgMenu">
            <el-image
              preview-teleported
              :src="item.messageData.payload.remoteMediaUrl"
              :preview-src-list="[item.messageData.payload.remoteMediaUrl]"
            />
          </div>
          <!-- 位置4-->
          <div v-if="item.messageData.payload.type == 4" class="msgText tipText msgMenu">
            <p>位置信息（请至客户端查看）</p>
            <p>
              <icon name="el-icon-location" />
              {{ item.messageData.payload.searchableContent }}
            </p>
          </div>
          <!-- 文件5-->
          <div v-if="item.messageData.payload.type == 5" class="msgText cursorText msgMenu">
            <p @click="fileOpen(item.messageData.payload)">
              <icon name="el-icon-document" />
              {{ item.messageData.payload.searchableContent }}
            </p>
          </div>
          <!-- 视频消息6-->
          <div v-if="item.messageData.payload.type == 6" class="msgText tipText msgMenu">
            <p>视频（请至客户端查看）</p>
          </div>
          <!-- 动态表情7-->
          <div v-if="item.messageData.payload.type == 7" class="msgText tipText msgMenu">
            <p>动态表情（请至客户端查看）</p>
          </div>
          <!-- 图片消息混排8-->
          <div v-if="item.messageData.payload.type == 8" class="msgText tipText msgMenu">
            <p>图片消息混排（请至客户端查看）</p>
          </div>
          <!-- 名片10-->
          <div v-if="item.messageData.payload.type == 10" class="msgText tipText msgMenu">
            <p>名片（请至客户端查看）</p>
            <!-- <p>{{ item.messageData.payload.content }}</p> -->
          </div>
          <!-- 撤回80-->
          <div v-if="item.messageData.payload.type == 80" class="tipText">
            <p>
              {{
                useWFlowStore().loginUser.id == item.senderInfo.userId
                  ? '你'
                  : rowInfo.targetType == 2
                    ? item.senderInfo.displayName
                    : '对方'
              }}撤回了一条消息
            </p>
          </div>
          <!-- 创建群组消息104-->
          <div v-if="item.messageData.payload.type == 104" class="tipText">
            <p>{{ item.senderInfo.displayName }}创建群组</p>
          </div>
          <!-- 添加群组成员105-->
          <div v-if="item.messageData.payload.type == 105" class="tipText">
            <p>{{ item.senderInfo.displayName }}添加群组成员</p>
          </div>
          <!-- 移出群组成员106-->
          <div v-if="item.messageData.payload.type == 106" class="tipText">
            <p>{{ item.senderInfo.displayName }}移出群组成员</p>
          </div>
          <!-- 退出群组107-->
          <div v-if="item.messageData.payload.type == 107" class="tipText">
            <p>{{ item.senderInfo.displayName }}退出群组</p>
          </div>
          <!-- 解散群组108-->
          <div v-if="item.messageData.payload.type == 108" class="tipText">
            <p>{{ item.senderInfo.displayName }}解散群组</p>
          </div>
          <!-- 转让群组109-->
          <div v-if="item.messageData.payload.type == 109" class="tipText">
            <p>{{ item.senderInfo.displayName }}转让群组</p>
          </div>
          <!-- 修改群组名称110-->
          <div v-if="item.messageData.payload.type == 110" class="tipText">
            <p>{{ item.senderInfo.displayName }}修改群组名称</p>
          </div>
          <!-- 修改群昵称 -->
          <div v-if="item.messageData.payload.type == 111" class="tipText">
            <p>{{ item.senderInfo.name }}修改群昵称{{ item.senderInfo.displayName }}</p>
          </div>

          <!-- 语音视频发起400-->
          <div v-if="item.messageData.payload.type == 400" class="msgText tipText msgMenu">
            <p>
              <icon name="el-icon-phone" />
              {{ item.messageData.payload.pushContent }}（请至客户端查看）
            </p>
          </div>
          <!-- 语音视频401-->
          <div v-if="item.messageData.payload.type == 401" class="msgText tipText msgMenu">
            <p>
              <icon name="el-icon-phone" />
              网络电话接听（请至客户端查看）
            </p>
          </div>
          <!-- 语音视频402-->
          <div v-if="item.messageData.payload.type == 402" class="msgText tipText msgMenu">
            <p>
              <icon name="el-icon-phone" />
              网络电话结束（请至客户端查看）
            </p>
          </div>
          <!-- 语音视频403-->
          <div v-if="item.messageData.payload.type == 403" class="msgText tipText msgMenu">
            <p>
              <icon name="el-icon-phone" />
              网络电话信令（请至客户端查看）
            </p>
          </div>
          <!-- 语音视频404-->
          <div v-if="item.messageData.payload.type == 404" class="msgText tipText msgMenu">
            <p>
              <icon name="el-icon-phone" />
              网络电话变更（请至客户端查看）
            </p>
          </div>

          <!-- 工单提醒1001 -->
          <div v-if="item.messageData.payload.type == 1001" class="msgPush msgMenu">
            <p class="p1">{{ parseLabel(item.messageData.payload.content) }}</p>
            <p class="p2">{{ parseTitle(item.messageData.payload.content) }}</p>
            <div class="p3-div">
              <p
                class="p3"
                v-for="(item, index) in parseContent(item.messageData.payload.content)"
                :key="index"
              >
                {{ item }}
              </p>
            </div>
            <el-button
              type="primary"
              @click="handleDetail(item.messageData.payload.content)"
              v-if="parseUrl(item.messageData.payload.content)"
              >查看详情
            </el-button>
          </div>
          <!-- 转发-评论1002 -->
          <div v-if="item.messageData.payload.type == 1002" class="msgPush msgMenu">
            <p class="p4">{{ parseComment(item.messageData.payload.content) }}</p>
            <p class="p5">
              {{ parseTitle(item.messageData.payload.content) }}
              <span @click="handleDetail(item.messageData.payload.content)">{{
                parseProcess(item.messageData.payload.content)
              }}</span>
            </p>
          </div>

          <!-- 入职档案-1003 -->
          <div
            class="msgPush"
            @click="handleRuZhi"
            style="margin-bottom: 10px; padding: 0; cursor: pointer"
            v-if="item.messageData.payload.type === 1003"
          >
            <div class="ruzhi-img-text flex-col"></div>
            <div style="padding: 0 13px">
              <div style="margin-top: 24px">
                <p class="p2">{{ parseTitle(item.messageData.payload.content) }}</p>
              </div>
              <div class="p3-div">
                <p
                  class="p3"
                  v-for="(item, index) in parseContent(item.messageData.payload.content)"
                  :key="index"
                >
                  {{ item }}
                </p>
              </div>
              <div class="ruzhi-button center-align" style="justify-content: space-between">
                <div>立即完善个人档案</div>
                <img src="@/assets/image/arrow_right.png" />
              </div>
            </div>
          </div>

          <!-- 入职签约-1005 -->
          <div
            class="msgPush"
            @click="handleSign(item.messageData.payload.content)"
            style="margin-bottom: 10px; padding: 0; cursor: pointer"
            v-if="item.messageData.payload.type === 1005"
          >
            <div class="sign-img-text flex-col"></div>
            <div style="padding: 0 13px">
              <div style="margin-top: 24px">
                <p class="p2">{{ parseTitle(item.messageData.payload.content) }}</p>
              </div>
              <div class="p3-div">
                <p
                  class="p3"
                  v-for="(item, index) in parseContent(item.messageData.payload.content)"
                  :key="index"
                >
                  {{ item }}
                </p>
              </div>

              <div class="ruzhi-button center-align" style="justify-content: space-between">
                <div>立即签约</div>
                <img src="@/assets/image/arrow_right.png" />
              </div>
            </div>
          </div>

          <!-- 合并转发消息 -->
          <div
            v-if="item.messageData.payload.type === 11"
            class="msgPush forward-message"
            @click="handleShowForward(item.messageData.payload)"
          >
            <div class="forward-title">{{ item.messageData.payload.content }}</div>
            <div class="forward-content">{{ item.messageData.payload.searchableContent }}</div>
            <div class="forward-footer">
              <span>聊天记录</span>
            </div>
          </div>

          <!-- 已读未读状态及数量展示 -->
          <div
            class="tip"
            v-if="
              item.messageData.payload.type != 80 &&
              item.messageData.payload.type != 104 &&
              item.messageData.payload.type != 105 &&
              item.messageData.payload.type != 106 &&
              item.messageData.payload.type != 107 &&
              item.messageData.payload.type != 108 &&
              item.messageData.payload.type != 109 &&
              item.messageData.payload.type != 110
            "
          >
            <span
              class="readStatusSpan"
              v-if="
                rowInfo.targetType == 1 && useWFlowStore().loginUser.id == item.senderInfo.userId
              "
              :class="{
                specialBlue: item.messageData.readStatus == 0,
                specialGrey: item.messageData.readStatus == 1
              }"
            >
              {{
                item.messageData.readStatus == 0
                  ? '未读'
                  : item.messageData.readStatus == 1
                    ? '已读'
                    : ''
              }}
            </span>
            <span
              class="readStatusSpan readStatusSpan-click"
              v-if="
                rowInfo.targetType == 2 && useWFlowStore().loginUser.id == item.senderInfo.userId
              "
              @click="handleUnRead(item.messageData)"
              :class="{
                specialBlue: item.messageData.unreadUser,
                specialGrey: !item.messageData.unreadUser
              }"
            >
              {{
                item.messageData.unreadUser
                  ? item.messageData.unreadUser.split(',').length + '人未读'
                  : '全部已读'
              }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div v-if="Object.keys(rowInfo).length == 0 && !isInDialog" class="placeholderImage">
      <img src="@/assets/image/msg-bg.png" />
      没有选择对话窗口哦～
    </div>

    <!-- 合并转发消息查看弹窗 -->
    <el-dialog
      v-model="forwardDialogVisible"
      :title="forwardDialogTitle"
      width="40%"
      :fullscreen="false"
      style="height: 60vh"
    >
      <DialogComponent :detail-list="forwardMessages" :is-in-dialog="true" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, nextTick, watch, defineProps, defineEmits } from 'vue'
import { useWFlowStore } from '@/store/modules/wflow'
import { toTime } from '@/utils/date'
// 定义props
const props = defineProps({
  detailList: {
    type: Array,
    default: () => []
  },
  rowInfo: {
    type: Object,
    default: () => ({})
  },
  isMultiSelectMsg: {
    type: Boolean,
    default: false
  },
  selectedMsg: {
    type: Array,
    default: () => []
  },
  // 新增：是否在弹窗模式下使用
  isInDialog: {
    type: Boolean,
    default: false
  }
})

// 定义emit
const emit = defineEmits([
  'handleAvatar',
  'showMsgMenu',
  'handleSelectMsg',
  'textClick',
  'fileOpen',
  'handleDetail',
  'handleRuZhi',
  'handleSign',
  'handleShowForward',
  'handleUnRead'
])

// 滚动目标引用
const scrollTarget = ref(null)

// 工单1001类型 解析数据
const parseLabel = (content) => {
  try {
    const parsedContent = JSON.parse(content)
    if (parsedContent && parsedContent.label) {
      return parsedContent.label
    }
  } catch (error) {}
}

const parseTitle = (content) => {
  try {
    const parsedContent = JSON.parse(content)
    if (parsedContent && parsedContent.title) {
      return parsedContent.title
    }
  } catch (error) {}
}

const parseContent = (content) => {
  try {
    const parsedContent = JSON.parse(content)
    if (parsedContent && parsedContent.content) {
      return parsedContent.content.split('\\split')
    }
  } catch (error) {}
}

const parseComment = (content) => {
  try {
    const parsedContent = JSON.parse(content)
    if (parsedContent && parsedContent.content) {
      return parsedContent.content
    }
  } catch (error) {}
}

const parseProcess = (content) => {
  try {
    const parsedContent = JSON.parse(content)
    if (parsedContent && parsedContent.process) {
      return parsedContent.process
    }
  } catch (error) {}
}

const parseUrl = (content) => {
  try {
    const parsedContent = JSON.parse(content)
    if (parsedContent && parsedContent.url) {
      return parsedContent.url
    }
  } catch (error) {}
}

// 检查是否是链接
const isLink = (text) => {
  if (!text) return false
  // 简单的链接检测正则表达式
  const urlRegex = /(https?:\/\/[^\s]+)/g
  return urlRegex.test(text)
}

// 事件处理函数，通过emit传递给父组件
const handleAvatar = (info) => {
  emit('handleAvatar', info)
}

const showMsgMenu = (event) => {
  emit('showMsgMenu', event)
}

const handleSelectMsg = (val, item) => {
  emit('handleSelectMsg', val, item)
}

const textClick = (content, base64Data) => {
  emit('textClick', content, base64Data)
}

const fileOpen = (payload) => {
  emit('fileOpen', payload)
}

const handleDetail = (content) => {
  emit('handleDetail', content)
}

const handleRuZhi = () => {
  emit('handleRuZhi')
}

const handleSign = (content) => {
  emit('handleSign', content)
}

const handleUnRead = (messageData) => {
  emit('handleUnRead', messageData)
}
const forwardDialogVisible = ref(false)
const forwardDialogTitle = ref('')
const forwardMessages = ref([])
//查看合并转发消息
const handleShowForward = (content) => {
  console.log('content.contentcontent.contentcontent.content', content.base64edData)
  //消息框的标题
  forwardDialogTitle.value = content.content

  //解码转发的消息
  const msgStr = decodeURIComponent(escape(atob(content.base64edData)))
  const sourceData = JSON.parse(msgStr)
  const targetArray = sourceData.ms.map((msItem) => {
    return {
      messageData: {
        messageId: msItem.uid,
        sender: msItem.from,
        conv: {
          type: 0,
          target: msItem.target,
          line: msItem.line
        },
        payload: {
          type: msItem.ctype,
          searchableContent: msItem.csc,
          pushContent: '',
          pushData: '',
          content: '',
          base64edData: msItem.cbc,
          mediaType: 0,
          remoteMediaUrl: msItem.ctype === 3 ? msItem.mru : '',
          previewMediaUrl: null,
          persistFlag: 0,
          expireDuration: 0,
          mentionedType: 0,
          mentionedTarget: [],
          extra: ''
        },
        toUsers: [],
        timestamp: msItem.serverTime,
        client: null,
        readStatus: 1,
        readUser: null,
        unreadUser: null,
        unreadMessageTotal: 0,
        isSender: 0
      },
      senderInfo: {
        userId: msItem.from,
        name: msItem.fromName,
        password: null,
        displayName: msItem.fromName,
        portrait: msItem.fromAvatar,
        gender: 0,
        mobile: '',
        email: '',
        address: '',
        company: '',
        social: '',
        extra: '',
        type: 0,
        updateDt: msItem.serverTime
      },
      mySend: false
    }
  })
  console.log('targetArraytargetArraytargetArray', targetArray)
  // 将解析后的消息数据保存到 forwardMessages 中
  forwardMessages.value = targetArray
  // 打开弹窗
  forwardDialogVisible.value = true
}
// 监听detailList变化，滚动到底部
watch(
  () => props.detailList,
  () => {
    nextTick(() => {
      if (scrollTarget.value) {
        scrollTarget.value.scrollTo(0, scrollTarget.value.scrollHeight)
        setTimeout(() => {
          scrollTarget.value.scrollTo(0, scrollTarget.value.scrollHeight)
        }, 300)
      }
    })
  },
  { deep: true }
)
</script>

<style lang="scss" scoped>
.contentDiv {
  height: calc(100vh - 180px);
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;
  background: #f1f2f6;

  // 弹窗模式下的样式
  &.dialog-mode {
    height: 650px;
  }
}

.placeholderImage {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 14px;

  img {
    width: 200px;
    margin-bottom: 20px;
  }
}

.msgDiv {
  display: flex;
  margin-bottom: 20px;
  position: relative;

  .div1-avatar {
    margin-right: 10px;
  }

  .div1 {
    width: 36px;
    height: 36px;
    background: #3370ff;
    color: #fff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
  }

  .msgText {
    background: #fff;
    color: #303133;
    box-sizing: border-box;
    padding: 10px 12px;
    border-radius: 4px;
    max-width: 100%;
    word-break: break-all;
    white-space: pre-wrap;
  }

  .tipText {
    color: #9099a9;

    p {
      display: flex;
    }

    .el-icon-location,
    .el-icon-phone {
      margin: 2px 3px 0 0;
    }
  }

  .tip {
    // margin-bottom: 4px;
    font-size: 12px;
    color: #909399;

    .span1 {
      margin-right: 15px;
    }
  }

  .tipSpecial {
    position: absolute;
    top: -18px;
    width: max-content;
    left: 0;
  }

  .rightColor {
    .msgText {
      background: #c9e7ff;
      color: #303133;
      white-space: pre-wrap;
    }

    .tipText {
      color: #9099a9;
    }
  }

  .cursorText {
    cursor: pointer;

    p {
      display: flex;
    }

    .el-icon-document {
      margin: 2px 3px 0 0;
    }
  }

  .cursorText:hover {
    color: #3370ff;
    text-decoration: underline;
  }

  .msgImg {
    max-width: 200px;

    .el-image {
      border-radius: 6px;
    }
  }

  .msgPush {
    background: #fff;
    color: #303133;
    box-sizing: border-box;
    padding: 15px 12px;
    height: fit-content;
    border-radius: 4px;
    width: 360px;

    .p1 {
      // color:#FFA826;
      padding: 0;
      margin-bottom: 6px;
      font-size: 15px;
    }

    .p2 {
      font-weight: bold;
      padding: 0;
      // margin-bottom: 20px;
      font-size: 15px;
    }

    .p3 {
      // font-weight: bold;
      padding: 0;
      font-size: 15px;
      line-height: 150%;
      white-space: pre-wrap;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
    }

    .p3-div {
      margin-top: 8px;
    }

    .p4 {
      padding: 0;
      font-size: 15px;
      line-height: 150%;
      white-space: pre-wrap;
      word-wrap: break-word;
      font-weight: bold;
    }

    .p5 {
      padding: 0;
      font-size: 12px;
      line-height: 150%;
      margin-top: 6px;
      color: #9099a9;
      position: relative;
      padding-left: 8px;

      span {
        // margin-left: 10px;
        color: #3370ff;
        font-weight: bold;
        cursor: pointer;
      }
    }

    .p5::before {
      position: absolute;
      left: 0;
      top: 0px;
      // bottom: 10px;
      width: 2px;
      height: 18px;
      content: '';
      background: #dedede;
    }

    .el-button {
      width: 100%;
      background: #3370ff;
      margin-top: 15px;
    }
  }

  // 聊天记录转发消息样式
  .forward-message {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    width: 280px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .forward-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
      line-height: 1.4;
    }

    .forward-content {
      font-size: 13px;
      color: #666;
      line-height: 1.5;
      margin-bottom: 10px;
      white-space: pre-wrap;
      word-wrap: break-word;

      // 限制显示行数，超出显示省略号
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 4;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .forward-footer {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding-top: 8px;
      border-top: 1px solid #e9ecef;

      span {
        font-size: 12px;
        color: #999;
        background: #f0f0f0;
        padding: 2px 8px;
        border-radius: 10px;
        display: inline-flex;
        align-items: center;

        &::before {
          content: '💬';
          margin-right: 4px;
          font-size: 10px;
        }
      }
    }
  }
}

.right-msg {
  box-sizing: border-box;
  max-width: 60%;
  position: relative;
}

.msgPositionRight {
  justify-content: end;
  flex-direction: row-reverse;

  .div1-avatar,
  .div1 {
    margin-right: 0;
    margin-left: 10px;
  }

  .tip {
    text-align: right;
  }

  .right-msg {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .tip {
      margin-right: 0;
    }

    .tipSpecial {
      right: 0;
      left: auto;
    }
  }
}

.justifyContentDiv {
  justify-content: center;

  .tip {
    text-align: center;
  }

  .tipSpecial {
    position: relative;
    top: 0;
    width: -webkit-fill-available;
  }
}

.specialBlue {
  color: #3370ff;
}

.specialGrey {
  color: #c0c4cc;
}

.readStatusSpan {
  margin-top: 2px;
  display: inline-block;
}

.readStatusSpan-click {
  cursor: pointer;
}

.ruzhi-img-text {
  background-image: url('@/assets/image/ruzhi.png');
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  height: 165px;
  width: 100%;
}

.sign-img-text {
  background-image: url('@/assets/image/employmentContract.jpg');
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  height: 165px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  width: 100%;
}

.ruzhi-img-title {
  width: 149px;
  height: 88px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: center;
  line-height: 44px;
  margin-left: 40px;
}

.ruzhi-button {
  height: 40px;
  color: rgba(51, 112, 255, 1);
  font-size: 15px;
  line-height: 40px;
  margin: 9px 0 10px 0px;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.center-align {
  display: flex;
  align-items: center;
}

.msg-checkbox {
  position: absolute;
  left: -24px;
  top: 50%;
  transform: translateY(-50%);
}
</style>
