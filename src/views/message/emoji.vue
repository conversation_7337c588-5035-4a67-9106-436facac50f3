<!--
 * @Descripttion: 表情包组件
 * @version: v1.0.0
 * @Author: wangfu
 * @Date: 2022-08-05 09:29:57
 * @LastEditors: wangfu
 * @LastEditTime: 2022-08-10 14:44:00
-->
<template>
  <div class="emoji-box">
    <ul>
      <li
        class="emojiList"
        v-for="(item, index) in emojis"
        :key="index"
        @click.stop="handleEmoji(item)"
      >
        {{ item.text }}
      </li>
    </ul>
  </div>
</template>
<script>
const emojis = [
  "😀",
  "😄",
  "😅",
  "🤣",
  "😂",
  "😉",
  "😊",
  "😜",
  "😝",
  "😏",
  "😒",
  "🙄",
  "😪",
  "😔",
  "😴",
  "😷",
  "🤮",
  "😵",
  "🤠",
  "🙁",
  "☹️",
  "🥵",
  "😎",
  "😮",
  "😰",
  "😭",
  "😱",
  "😩",
  "😡",
  "🤓",
  "🥳",
  "🤚",
  "👍",
  "👎",
  "👌",
  "👏",
  "🙏",
  "💪",
  "🐜",
  "🍚",
  "🌏",
  "🚅",
  "🚕",
  "💰",
  "🔨",
];

export default {
  name: "TEmoji",
  data() {
    return {};
  },
  created() {
    this.emojis = emojis.map((emoji) => ({ text: emoji }));
  },
  methods: {
    handleEmoji(item){
        this.$emit('chooseEmoji',item.text)
    },
  },
};
</script>
<style lang='scss' scoped>
.emoji-box {
  position: absolute;
  width: 300px;
  background-color: #fff;
  top: -187px;
  left: 0px;
  box-sizing: border-box;
  padding: 5px;
  box-shadow: 0 -3px 8px 2px rgba(125, 143, 155, 0.1);
  & ul {
    width: 100%;
    height: 165px;
    overflow: scroll;
    margin-block-start: 0;
    display: block;
    list-style-type: disc;
    margin-block-end: 0px;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 0px;
    &::-webkit-scrollbar {
            width: 0 !important
          }
    & li{
        list-style-type:none;
        float: left;
        padding: 2px;
        cursor: pointer;
        font-size: 18px;
        &:hover{
            background-color: #f2f2f2;
        }
    }
  }
}
</style>