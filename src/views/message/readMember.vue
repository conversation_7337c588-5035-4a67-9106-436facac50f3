<template>
  <div>
    <el-dialog v-model="dialog" title="消息接收人列表" :before-close="drawerClose" class="custom-dialog" :width="width">
      <div class="contentDiv">
        <div class="f-div">
          <p class="tipP"><b>{{ unReadList.length }}</b>人未读</p>
          <div v-for="(item, index)  in unReadList" :key="index" class="div">
            <avatar :size="32" :name="item.displayName" :src="item.portrait" class="search-avatar" />
          </div>
        </div>
        <div class="f-div">
          <p class="tipP"><b>{{ readList.length }}</b>人已读</p>
          <div>
            <div v-for="(item, index)  in readList" :key="index" class="div">
              <avatar :size="32" :name="item.displayName" :src="item.portrait" class="search-avatar" />
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup type="ts">
import * as messageApi from '@/api/message/index'
const loading = ref(true)//默认加载
const dialog = ref(false)//弹窗默认false

const width = ref('29%')
const screenWidth = ref(null)

const readList = ref([])
const unReadList = ref([])
const open = async (row) => {
  try {
    readList.value = []
    unReadList.value = []
    dialog.value = true
    screenWidth.value = document.body.clientWidth
    window.onresize = () => {
      screenWidth.value = document.body.clientWidth
    }

    const res = await messageApi.readMembers({ readUser: row.readUser, unreadUser: row.unreadUser })
    readList.value = res.data.readUserList
    unReadList.value = res.data.unreadUserList
  } finally {
    loading.value = false
  }
}

watch(screenWidth, (newValue) => {
  if (newValue > 1600) {
    width.value = '29%'
  } else if (newValue >= 1200) {
    width.value = '48%'
  } else if (newValue >= 700) {
    width.value = '68%'
  } else if (newValue < 700) {
    width.value = '95%'
  }
})



function drawerClose() {
  dialog.value = false
}

defineExpose({
  open, drawerClose
})
</script>
<style lang="less" scoped>
:deep(.el-dialog__body) {
  padding: 0;
  padding-bottom: 40px;
  max-height: 500px;
  overflow: auto;
}

.contentDiv {
  display: flex;
  position: relative;

  .f-div {
    width: 100%;
    box-sizing: border-box;
    margin: 15px;
  }

  .div {
    width: 100%;
    padding: 8px 10px;
    margin-bottom: 6px;
    border-radius: 6px;
    cursor: pointer;
    box-sizing: border-box;
  }

  .div:hover {
    background: #f1f2f5;
  }

}

.contentDiv::after {
  position: absolute;
  content: '';
  width: 1px;
  // height: 100%;
  left: 50%;
  top: 0;
  background: #ebeef5;
  bottom: -40px;
}

.tipP {
  margin: 0;
  margin-bottom: 6px;
  padding-left: 10px;

  b {
    font-weight: bold;
    font-size: 25px;
    margin-right: 8px;
  }
}

.search-avatar {
  .a-img>div {
    font-size: 12px
  }
}
</style>
<style>
.search-avatar .a-img>div {
  font-size: 12px !important
}
</style>