<template>
  <div class="main-view">
    <div class="segment-switch">
      <div class="segment-wrapper">
        <div class="segment-bg"
          :class="{ 'segment-bg-create': selected === 'create', 'segment-bg-join': selected === 'join' }"></div>
        <div class="segment" :class="{ active: selected === 'create' }" @click="segmentSwitch">
          <img v-if="selected === 'create'" class="icon" :src="createIcon">
          创建新的企业/团队
        </div>
        <div class="segment" :class="{ active: selected === 'join' }" @click="segmentSwitch">
          <img v-if="selected === 'join'" class="icon" :src="joinIcon">
          加入已有的企业/团队
        </div>
      </div>
      <transition name="fade-slide" mode="out-in">
        <!-- 创建新的企业/团队 -->
        <el-form v-if="selected === 'create'" ref="createFormRef" :rules="createValidateRules" :model="createForm"
          class="content" label-width="100px" label-position="left">
          <el-form-item label="企业logo：" class="form-item" prop="logo" required :show-message="false">
            <el-upload action="#" :auto-upload="false" :file-list="logoList" :limit="1" list-type="picture-card"
              :on-change="onLogoImgUpdate" :on-remove="onLogoImgUpdate" :on-preview="onPreview"
              :class="{ 'logo-upload': logoList.length > 0 }">
              <el-icon v-if="logoList.length == 0" :size="14">
                <Plus />
              </el-icon>
            </el-upload>
            <el-image-viewer v-if="previewVisible" :url-list="previewImgList" @close="previewVisible = false" />
          </el-form-item>
          <el-form-item label="营业执照：" class="form-item" prop="licenseUrl" required :show-message="false">
            <el-upload action="#" :auto-upload="false" :file-list="licenseList" :limit="1" list-type="picture-card"
              :on-change="onLicenseImgUpdate" :on-remove="onLicenseImgUpdate" :on-preview="onPreview"
              :class="{ 'license-upload': licenseList.length > 0 }">
              <el-icon v-if="licenseList.length == 0" :size="14">
                <Plus />
              </el-icon>
            </el-upload>
            <el-image-viewer v-if="previewVisible" :url-list="previewImgList" @close="previewVisible = false" />
          </el-form-item>
          <el-form-item label="企业名：" class="form-item" prop="name" required :show-message="false">
            <el-input v-model="createForm.name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="企业简称：" class="form-item" prop="shortName" required :show-message="false">
            <el-input v-model="createForm.shortName" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="联系人：" class="form-item" prop="contactName" required :show-message="false">
            <el-input v-model="createForm.contactName" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="联系手机：" class="form-item" prop="contactMobile" required>
            <el-input v-model="createForm.contactMobile" placeholder="请输入" type="number"></el-input>
          </el-form-item>
          <el-form-item label="办办账户：" class="form-item" prop="username" required>
            <el-input v-model="createForm.username" placeholder="请输入用于注册办办账户的手机号" type="number"></el-input>
          </el-form-item>
          <el-form-item label="办办密码：" class="form-item" prop="password" required>
            <el-input v-model="createForm.password" placeholder="请输入" type="password" show-password></el-input>
          </el-form-item>
        </el-form>
        <!-- 加入已有的企业/团队 -->
        <el-form v-else ref="joinFormRef" :rules="joinValidateRules" :model="joinForm" label-width="120px" label-position="left" class="content">
          <el-form-item label="员工姓名：" class="form-item" prop="nickname" required :show-message="false">
            <el-input v-model="joinForm.nickname" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="员工手机号：" class="form-item" prop="mobile" required>
            <el-input v-model="joinForm.mobile" placeholder="请输入用于注册办办账户的手机号"></el-input>
          </el-form-item>
          <el-form-item label="登录密码：" class="form-item" prop="password" required>
            <el-input v-model="joinForm.password" placeholder="请输入" type="password" show-password></el-input>
          </el-form-item>
          <el-form-item label="选择企业：" class="form-item" prop="tenantId" required :show-message="false">
            <el-select v-model="joinForm.tenantId" placeholder="请选择" filterable clearable popper-class="custom-option">
              <el-option v-for="(item, index) in entList" :key="index" :label="item.name" :value="item.id">
                <div class="custom-option-content">
                  <div style="display: flex; align-items: center;">
                    <el-image :src="(item.logo && item.logo.includes('http')) ? item.logo : entDefaultIcon" fit="cover"></el-image>
                    <span>{{ item.name }}</span>
                  </div>
                  <div class="line"></div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </transition>
    </div>
    <XButton :loading="selected === 'create' ? createRegisterLoading : joinRegisterLoading"
      :title="selected === 'create' ? '完成创建' : '确认'" class="w-[100%] commit-btn" type="primary"
      @click="onCommitClick()" />
    <el-button class="back-btn" link @click="toLogin" style="margin-left:0;">
      <el-icon>
        <ArrowLeft />
      </el-icon>
      返回登录页面
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { RegisterApi } from '@/api/login/register';
import createIcon from '@/assets/imgs/register_create_ent.png'
import joinIcon from '@/assets/imgs/register_join_ent.png'
import entDefaultIcon from '@/assets/imgs/logoBlue.png';
import { ArrowLeft, Plus } from '@element-plus/icons-vue';
import { FormInstance, FormRules } from 'element-plus';

const message = useMessage()
const selected = ref<'create' | 'join'>('create');

// 创建企业及注册 有关
const createRegisterLoading = ref(false)
const createFormRef = ref<FormInstance>()
const createForm = reactive({
  logo: '',
  licenseUrl: '',
  name: '',
  shortName: '',
  contactName: '',
  contactMobile: '',
  username: '',
  password: ''
})

// 加入企业及注册 有关
const joinRegisterLoading = ref(false)
const joinFormRef = ref<FormInstance>()
const joinForm = reactive({
  nickname: '',
  mobile: '',
  username: '',
  password: '',
  tenantId: undefined
})

const entList = ref<{
  id: number,
  logo: string,
  name: string
}[]>([]) //企业列表
const logoList = ref<any[]>([]) //logo upload 绑定的fileList
const licenseList = ref<any[]>([]) //license upload 绑定的fileList
const previewImgList = ref<string[]>([])
const previewVisible = ref(false)

// 表单验证规则
const createValidateRules = reactive<FormRules>({
  contactMobile: [
    { required: true, message: '请输入企业联系人手机号', trigger: 'blur' },
    { min: 11, max: 11, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用于注册办办账户的手机号', trigger: 'blur' },
    { min: 11, max: 11, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入办办账户登录密码', trigger: 'blur' },
    { min: 6, message: '密码不能少于6位', trigger: 'blur' }
  ]
})

// 表单验证规则
const joinValidateRules = reactive<FormRules>({
  mobile: [
    { required: true, message: '请输入用于注册办办账户的手机号', trigger: 'blur' },
    { min: 11, max: 11, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入办办账户登录密码', trigger: 'blur' },
    { min: 6, message: '密码不能少于6位', trigger: 'blur' }
  ]
})

// 创建 加入 注册切换
const segmentSwitch = () => {
  selected.value = selected.value === 'create' ? 'join' : 'create'
  resetForm()
}

// logo图片组件 - 变动
const onLogoImgUpdate = (_: any, list: any) => {
  logoList.value = list
}

// 营业执照图片组件 - 变动
const onLicenseImgUpdate = (_: any, list: any) => {
  licenseList.value = list
}

// 图片组件 - 预览
const onPreview = (file: any) => {
  previewImgList.value = [file.url]
  previewVisible.value = true
}

// 确定按钮点击
const onCommitClick = async () => {
  // 创建企业及注册
  if (selected.value === 'create') {
    if (!createFormRef) return
    if (logoList.value.length == 0) {
      return message.warning('请选择企业logo图片')
    }
    if (licenseList.value.length == 0) {
      return message.warning('请选择营业执照图片')
    }
    if (createForm.name.trim().length == 0) {
      return message.warning('请填写企业名')
    }
    if (createForm.shortName.trim().length == 0) {
      return message.warning('请填写企业简称')
    }
    if (createForm.contactName.trim().length == 0) {
      return message.warning('请填写企业联系人')
    }
    if (createForm.contactMobile.trim().length == 0) {
      return message.warning('请填写企业联系人手机')
    }
    if (createForm.username.trim().length == 0) {
      return message.warning('请填写注册办办账户的手机号')
    }
    if (createForm.password.trim().length == 0) {
      return message.warning('请填写办办账户登录密码')
    }
    await createFormRef.value!.validate((valid) => {
      if (!valid) return
    })
    await message.confirm('确定要 创建该企业并注册账户 吗？')
    createRegisterLoading.value = true
    handleImageUpload()
  }
  // 加入企业及注册
  else {
    if (!joinFormRef) return
    if (joinForm.nickname.trim().length == 0) {
      return message.warning('请填写员工姓名')
    }
    if (joinForm.mobile.trim().length == 0) {
      return message.warning('请填写用于注册办办账户的手机号')
    }
    if (joinForm.password.trim().length == 0) {
      return message.warning('请填写办办账户登录密码')
    }
    if (!joinForm.tenantId) {
      return message.warning('请选择要加入的企业')
    }
    await joinFormRef.value!.validate((valid) => {
      if (!valid) return
    })
    await message.confirm('确定要提交 注册账户并加入该企业 的申请吗？')
    joinRegisterLoading.value = true
    handleCommit()
  }
}

// 处理图片上传
const handleImageUpload = () => {
  const uploadQueue = <any>[]
  logoList.value.forEach((e: any) => {
    uploadQueue.push(RegisterApi.uploadFile(<any>{ file: e.raw }))
  })
  licenseList.value.forEach((e: any) => {
    uploadQueue.push(RegisterApi.uploadFile(<any>{ file: e.raw }))
  })
  Promise.all(uploadQueue).then((res: any[]) => {
    if (res[0].code != 0) {
      ElMessage.error("logo图片上传失败，请尝试重新提交")
      createRegisterLoading.value = false
      return
    }
    if (res[1].code != 0) {
      ElMessage.error("营业执照图片上传失败，请尝试重新提交")
      createRegisterLoading.value = false
      return
    }
    createForm.logo = res[0].data.url
    createForm.licenseUrl = res[1].data.url

    handleCommit()
  }).catch(() => {
    ElMessage.error("图片上传发生错误，请尝试重新提交")
    createRegisterLoading.value = false
  })
}

// 注册接口处理
const handleCommit = async () => {
  // 创建企业及注册
  if (selected.value === 'create') {
    createFormRef.value?.clearValidate()
    try {
      await RegisterApi.registerAndCreateEnt(createForm)
      message.success('注册成功，您可以使用新账号登录办办了')
      if (toLogin) toLogin()
      resetForm()
    } catch (e: any) {
      message.error(e.msg ?? '注册失败，请尝试重新提交')
    } finally {
      createRegisterLoading.value = false
    }
  }
  // 加入企业及注册
  else {
    joinFormRef.value?.clearValidate()
    try {
      joinForm.username = joinForm.mobile
      await RegisterApi.registerAndJoinEnt(joinForm)
      message.success('申请提交成功，您的申请将会有人工进行审核，请等待审核结果通知')
      if (toLogin) toLogin()
      resetForm()
    } catch (e: any) {
      message.error(e.msg ?? '注册失败，请尝试重新提交')
    } finally {
      joinRegisterLoading.value = false
    }
  }
}

// 重置表单
const resetForm = () => {
  if (createFormRef) {
    createFormRef.value?.resetFields()
    createFormRef.value?.clearValidate()
  }
  if (joinFormRef) {
    joinFormRef.value?.resetFields()
    joinFormRef.value?.clearValidate()
  }
}

// 返回登录页
const toLogin = inject<() => {}>('switchLoginRegister')

// 请求企业列表数据
const reqEntList = () => {
  RegisterApi.getTenantList().then(res => {
    entList.value = res
  })
}

// 挂载
onMounted(() => {
  reqEntList()
})

</script>

<style lang="less" scoped>
.main-view {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.segment-switch {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.segment-wrapper {
  position: relative;
  display: flex;
  gap: 20px;
  overflow: hidden;
  height: 40px;
}

.segment-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 210px;
  height: 100%;
  border-radius: 40px;
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.segment-bg-create {
  transform: translateX(0);
  background-color: #f0f7ff;
}

.segment-bg-join {
  transform: translateX(230px);
  background-color: #effaf4;
}

.segment {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 170px;
  padding: 10px 20px;
  cursor: pointer;
  z-index: 1;
  font-size: 14px;
  color: #909399;
  border: 1px solid #EBEEF5;
  border-radius: 40px;
}

.active {
  border: 0;
  font-weight: bold;
  color: #303133;
}

.icon {
  margin-right: 10px;
}

.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.content {
  width: 440px;
  height: 540px;
  margin: 35px 0 40px 0;

  .form-item {
    margin: 0 0 24px 0;
    display: flex;
    align-items: center;
    justify-content: center;

    :deep(.el-upload--picture-card) {
      width: 60px;
      height: 60px;
      background: #fff;
      border: 1px dashed #DCDFE6;
      border-radius: 4px;
    }

    :deep(.el-upload--picture-card:hover) {
      border: 1px dashed #ccc;
    }

    .logo-upload,
    .license-upload {
      height: 60px;
    }

    :deep(.logo-upload .el-upload--picture-card) {
      display: none;
    }

    :deep(.license-upload .el-upload--picture-card) {
      display: none;
    }

    :deep(.el-upload-list--picture-card .el-upload-list__item) {
      height: 60px;
      width: 60px;
      margin-bottom: 0;
    }

    :deep(.el-upload-list--picture-card .el-upload-list__item-actions span+span) {
      margin-left: 5px;
    }

    .el-input {
      height: 40px;
    }
  }
}

.custom-option .el-select-dropdown__item {
  height: 56px;
  line-height: 56px;

  .custom-option-content {
    display: flex;
    flex-direction: column;

    .el-image {
      width: 32px;
      height: 32px;
      margin-right: 15px;
      border-radius: 3px;
    }

    .line {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 90%;
      height: 1px;
      margin: 0 5%;
      background-color: #DCDFE6;
    }
  }
}

.commit-btn {
  background: #3370ff;
}

.back-btn {
  margin-top: 16px;
  font-size: 12px;
  color: #909399;
}
</style>
