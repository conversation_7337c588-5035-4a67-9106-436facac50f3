<template>
  <div>

    <div class="center-view">
      <img style="width:48px;height:48px;" src="@/assets/image/appLogo.png" />
    </div>
    <div class="center-view" style="font-weight: 500;font-size: 22px;color: #303133;line-height: 30px;margin-top: 16px;">
      登录诺鑫办办
    </div>
    <div class="center-view" style="font-size: 16px;color: #303133;line-height: 22px;margin-top: 6px;margin-bottom:18px;">
      继续使用诺鑫办办管理后台
    </div>



    
  </div>
</template>
<script lang="ts" setup>
import { LoginStateEnum, useLoginState } from './useLogin'

defineOptions({ name: 'LoginFormTitle' })

const { t } = useI18n()

const { getLoginState } = useLoginState()

const getFormTitle = computed(() => {
  const titleObj = {
    [LoginStateEnum.RESET_PASSWORD]: t('sys.login.forgetFormTitle'),
    [LoginStateEnum.LOGIN]: t('sys.login.signInFormTitle'),
    [LoginStateEnum.REGISTER]: t('sys.login.signUpFormTitle'),
    [LoginStateEnum.MOBILE]: t('sys.login.mobileSignInFormTitle'),
    [LoginStateEnum.QR_CODE]: t('sys.login.qrSignInFormTitle'),
    [LoginStateEnum.SSO]: t('sys.login.ssoFormTitle')
  }
  return titleObj[unref(getLoginState)]
})
</script>
