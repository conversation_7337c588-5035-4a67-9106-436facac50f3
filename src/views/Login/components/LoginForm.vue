<template>
  <el-form
    v-show="getShow && !isForgetPassword"
    ref="formLogin"
    :model="loginData.loginForm"
    :rules="LoginRules"
    class="login-form"
    label-position="top"
    label-width="120px"
    size="large"
  >
    <el-row style="margin-right: -10px; margin-left: -10px">
      <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item>
          <LoginFormTitle style="width: 100%" />
        </el-form-item>
      </el-col>
      <!-- <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item v-if="loginData.tenantEnable === 'true'" prop="tenantName">
          <el-input v-model="loginData.loginForm.tenantName" :placeholder="t('login.tenantNamePlaceholder')"
            :prefix-icon="iconHouse" link type="primary" />
        </el-form-item>
      </el-col> -->
      <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item prop="username">
          <el-input
            v-model="loginData.loginForm.username"
            :placeholder="t('login.usernamePlaceholder')"
            :prefix-icon="iconAvatar"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item prop="password">
          <el-input
            v-model="loginData.loginForm.password"
            :placeholder="t('login.passwordPlaceholder')"
            :prefix-icon="iconLock"
            show-password
            type="password"
            @keyup.enter="getCode()"
          />
        </el-form-item>
      </el-col>

      <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item>
          <XButton
            :loading="loginLoading"
            :title="t('login.login')"
            class="w-[100%] custom-btn"
            type="primary"
            @click="getCode()"
          />
        </el-form-item>
      </el-col>
      <el-col
        :span="24"
        style="padding-right: 10px; padding-left: 10px; margin-top: -20px;"
      >
        <el-form-item>
          <el-row justify="space-between" align="middle" style="width: 100%">
            <el-col :span="6">
              <el-checkbox v-model="loginData.loginForm.rememberMe" class="custom-checkbox">
                {{ t('login.remember') }}
              </el-checkbox>
            </el-col>

            <el-col :span="3.6">
              <el-button style="font-size: 14px;color: #909399;line-height: 20px;margin-left: 10px;" link @click="findPassword">忘记密码</el-button>
            </el-col>

          </el-row>
        </el-form-item>
      </el-col>

      <el-col :span="24" class="center-view" style="padding-right: 10px; padding-left: 10px;margin-top:60px;">
        <el-button style="font-size: 14px;color: #909399;line-height: 20px;" link @click="toRegister">{{ t('login.accountRegister') }}</el-button>
      </el-col>
      <Verify
        ref="verify"
        :captchaType="captchaType"
        :imgSize="{ width: '400px', height: '200px' }"
        mode="pop"
        @success="handleLogin"
      />
      <!-- <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item>
          <el-row :gutter="5" justify="space-between" style="width: 100%">
            <el-col :span="8">
              <XButton :title="t('login.btnMobile')" class="w-[100%]" @click="setLoginState(LoginStateEnum.MOBILE)" />
            </el-col>
            <el-col :span="8">
              <XButton :title="t('login.btnQRCode')" class="w-[100%]" @click="setLoginState(LoginStateEnum.QR_CODE)" />
            </el-col>
            <el-col :span="8">
              <XButton :title="t('login.btnRegister')" class="w-[100%]"
                @click="setLoginState(LoginStateEnum.REGISTER)" />
            </el-col>
          </el-row>
        </el-form-item>
      </el-col> -->
      <!-- <el-divider content-position="center">{{ t('login.otherLogin') }}</el-divider>
      <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item>
          <div class="w-[100%] flex justify-between">
            <Icon
              v-for="(item, key) in socialList"
              :key="key"
              :icon="item.icon"
              :size="30"
              class="anticon cursor-pointer"
              color="#999"
              @click="doSocialLogin(item.type)"
            />
          </div>
        </el-form-item>
      </el-col> -->
      <!-- <el-divider content-position="center">萌新必读</el-divider>
      <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item>
          <div class="w-[100%] flex justify-between">
            <el-link href="https://doc.iocoder.cn/" target="_blank">📚开发指南</el-link>
            <el-link href="https://doc.iocoder.cn/video/" target="_blank">🔥视频教程</el-link>
            <el-link href="https://www.iocoder.cn/Interview/good-collection/" target="_blank">
              ⚡面试手册
            </el-link>
            <el-link href="http://static.yudao.iocoder.cn/mp/Aix9975.jpeg" target="_blank">
              🤝外包咨询
            </el-link>
          </div>
        </el-form-item>
      </el-col> -->
    </el-row>
  </el-form>

  <!-- 忘记密码表单 -->
  <el-form
    v-show="isForgetPassword"
    ref="forgetPasswordForm"
    :model="loginData.forgetPasswordForm"
    class="login-form"
    label-position="top"
    label-width="120px"
    size="large"
  >
    <el-row style="margin-right: -10px; margin-left: -10px">
      <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item>
          <div style="font-size: 24px; font-weight: 600; text-align: center; margin-bottom: 30px;">
            忘记密码
          </div>
        </el-form-item>
      </el-col>

      <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item prop="mobile">
          <el-input
            v-model="loginData.forgetPasswordForm.mobile"
            placeholder="请输入手机号"
            :prefix-icon="iconAvatar"
          />
        </el-form-item>
      </el-col>

      <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item prop="code">
          <el-row :gutter="20">
            <el-col :span="16">
              <el-input
                v-model="loginData.forgetPasswordForm.code"
                placeholder="请输入验证码"
                :prefix-icon="iconMessage"
              />
            </el-col>
            <el-col :span="8">
              <el-button
                :disabled="countdown > 0"
                class="w-[100%]"
                type="text"
                @click="getVerifyCode"
              >
                {{ countdown > 0 ? `${countdown}秒后重试` : '获取验证码' }}
              </el-button>
            </el-col>
          </el-row>
        </el-form-item>
      </el-col>

      <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item prop="password">
          <el-input
            v-model="loginData.forgetPasswordForm.password"
            placeholder="请输入新密码"
            :prefix-icon="iconLock"
            show-password
            type="password"
          />
        </el-form-item>
      </el-col>

      <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
        <el-form-item>
          <XButton
            title="重置密码"
            class="w-[100%] custom-btn"
            type="primary"
            @click="resetPassword"
          />
        </el-form-item>
      </el-col>

      <el-col :span="24" style="padding-right: 10px; padding-left: 10px; text-align: center;">
        <el-button class="back-btn" link @click="backToLogin" style="margin-left:0;">
          <el-icon>
            <ArrowLeft />
          </el-icon>
          返回登录页面
        </el-button>
      </el-col>
    </el-row>
  </el-form>
</template>
<script lang="ts" setup>
import { ElLoading } from 'element-plus'
import LoginFormTitle from './LoginFormTitle.vue'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import { useIcon } from '@/hooks/web/useIcon'

import * as authUtil from '@/utils/auth'
import { usePermissionStore } from '@/store/modules/permission'
import * as LoginApi from '@/api/login'
import { LoginStateEnum, useFormValid, useLoginState } from './useLogin'
import { ArrowLeft } from '@element-plus/icons-vue'

defineOptions({ name: 'LoginForm' })

const { t } = useI18n()
const message = useMessage()
const iconHouse = useIcon({ icon: 'ep:house' })
const iconAvatar = useIcon({ icon: 'ep:avatar' })
const iconLock = useIcon({ icon: 'ep:lock' })
const iconMessage = useIcon({ icon: 'ep:message' })
const formLogin = ref()
const { validForm } = useFormValid(formLogin)
const { setLoginState, getLoginState } = useLoginState()
const { currentRoute, push } = useRouter()
const permissionStore = usePermissionStore()
const redirect = ref<string>('')
const loginLoading = ref(false)
const verify = ref()
const captchaType = ref('blockPuzzle') // blockPuzzle 滑块 clickWord 点击文字

const getShow = computed(() => unref(getLoginState) === LoginStateEnum.LOGIN)

const LoginRules = {
  // tenantName: [required],
  username: [required],
  password: [required]
}
const loginData = reactive({
  isShowPassword: false,
  captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
  tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
  loginForm: {
    // tenantName: '诺鑫办办',
    username: '',
    password: '',
    captchaVerification: '',
    rememberMe: true // 默认记录我。如果不需要，可手动修改
  },
  forgetPasswordForm: {
    mobile: '',
    code: '',
    password: ''
  }
})

const isForgetPassword = ref(true)
const countdown = ref(0)
const timer = ref(null)

// 切换到忘记密码表单
const findPassword = () => {
  isForgetPassword.value = true
}

// 返回登录表单
const backToLogin = () => {
  isForgetPassword.value = false
}

// 获取验证码
const getVerifyCode = async () => {
  if (!loginData.forgetPasswordForm.mobile) {
    message.error('请输入手机号')
    return
  }
  try {
    await LoginApi.sendSmsCode({
      mobile: loginData.forgetPasswordForm.mobile,
      scene: 4
    })
    message.success('验证码发送成功')
    countdown.value = 60
    timer.value = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer.value)
      }
    }, 1000)
  } catch (error) {
    console.error('获取验证码失败:', error)
  }
}

// 重置密码
const resetPassword = async () => {
  if (!loginData.forgetPasswordForm.mobile || !loginData.forgetPasswordForm.code || !loginData.forgetPasswordForm.password) {
    message.error('请填写完整信息')
    return
  }
  try {
    const res = await LoginApi.resetPassword({
      mobile: loginData.forgetPasswordForm.mobile,
      code: loginData.forgetPasswordForm.code,
      password: loginData.forgetPasswordForm.password
    })
    if (res.userId) {
      message.success('密码重置成功')
      backToLogin()
    }
  } catch (error) {
    console.error('重置密码失败:', error)
  }
}

// 在组件销毁时清除定时器
onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})

// 在页面加载时从localStorage获取倒计时信息
onMounted(() => {
  const savedCountdown = localStorage.getItem('smsCountdown')
  const savedTimestamp = localStorage.getItem('smsTimestamp')
  if (savedCountdown && savedTimestamp) {
    const remainingTime = Math.floor((parseInt(savedCountdown) - (Date.now() - parseInt(savedTimestamp)) / 1000))
    if (remainingTime > 0) {
      countdown.value = remainingTime
      timer.value = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(timer.value)
          localStorage.removeItem('smsCountdown')
          localStorage.removeItem('smsTimestamp')
        } else {
          localStorage.setItem('smsCountdown', countdown.value.toString())
          localStorage.setItem('smsTimestamp', Date.now().toString())
        }
      }, 1000)
    }
  }
})

// const socialList = [
//   { icon: 'ant-design:wechat-filled', type: 30 },
//   { icon: 'ant-design:dingtalk-circle-filled', type: 20 },
//   { icon: 'ant-design:github-filled', type: 0 },
//   { icon: 'ant-design:alipay-circle-filled', type: 0 }
// ]

// 获取验证码
const getCode = async () => {
  // 情况一，未开启：则直接登录
  if (loginData.captchaEnable === 'false') {
    await handleLogin({})
  } else {
    // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
    // 弹出验证码
    verify.value.show()
  }
}
// 获取租户 ID
// const getTenantId = async () => {
//   if (loginData.tenantEnable === 'true') {
//     const res = await LoginApi.getTenantIdByName(loginData.loginForm.tenantName)
//     authUtil.setTenantId(res)
//   }
// }
// 记住我
const getLoginFormCache = () => {
  const loginForm = authUtil.getLoginForm()
  if (loginForm) {
    loginData.loginForm = {
      ...loginData.loginForm,
      username: loginForm.username ? loginForm.username : loginData.loginForm.username,
      password: loginForm.password ? loginForm.password : loginData.loginForm.password,
      rememberMe: loginForm.rememberMe
      // tenantName: loginForm.tenantName ? loginForm.tenantName : loginData.loginForm.tenantName
    }
  }
}
// 注册账号
const toRegister = inject<() => {}>('switchLoginRegister')
// 根据域名，获得租户信息
// const getTenantByWebsite = async () => {
//   const website = location.host
//   const res = await LoginApi.getTenantByWebsite(website)
//   if (res) {
//     loginData.loginForm.tenantName = res.name
//     authUtil.setTenantId(res.id)
//   }
// }
const loading = ref() // ElLoading.service 返回的实例
// 登录
const handleLogin = async (params) => {
  loginLoading.value = true
  try {
    // await getTenantId()
    const data = await validForm()
    if (!data) {
      return
    }
    loginData.loginForm.captchaVerification = params.captchaVerification
    const res = await LoginApi.login(loginData.loginForm)
    if (!res) {
      return
    }
    loading.value = ElLoading.service({
      lock: true,
      text: '正在加载系统中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    if (loginData.loginForm.rememberMe) {
      authUtil.setLoginForm(loginData.loginForm)
    } else {
      authUtil.removeLoginForm()
    }
    console.log(res, 'res')
    authUtil.setToken(res)
    authUtil.setTenantId(res.tenantId)
    if (!redirect.value) {
      redirect.value = '/index'
    }
    // 判断是否为SSO登录
    if (redirect.value.indexOf('sso') !== -1) {
      window.location.href = window.location.href.replace('/login?redirect=', '')
    } else {
      console.log("触发此处准备替换")
      const targetPath = '/#'+redirect.value

      window.location.href =window.location.href.replace(/\/#\/login.*$/, targetPath)


      // console.log("触发此处 redirect.value=",redirect.value)
      // console.log("触发此处 redirect=",redirect)
      // console.log("触发此处 permissionStore.addRouters[0].path=",permissionStore)
      // console.log("触发此处 permissionStore.addRouters[0].path=",permissionStore.addRouters[0].path)

      // const redirectPath = redirect.value;
      // console.log("触发此处 redirectPath=",redirectPath)
      // push({ path: redirectPath || permissionStore.addRouters[0].path })


      // const targetPath = redirect.value || permissionStore.addRouters[0].path
      // // 确保路径以 # 开头
      // const hashPath = targetPath.startsWith('#') ? targetPath : `#${targetPath}`
      // // 动态生成完整 URL
      // window.location.href = `${window.location.origin}/${hashPath}` // 跳转到完整 URL
    }
  } finally {
    loginLoading.value = false
    loading.value.close()
  }
}

// 社交登录
// const doSocialLogin = async (type: number) => {
//   if (type === 0) {
//     message.error('此方式未配置')
//   } else {
//     loginLoading.value = true
//     if (loginData.tenantEnable === 'true') {
//       // 尝试先通过 tenantName 获取租户
//       await getTenantId()
//       // 如果获取不到，则需要弹出提示，进行处理
//       if (!authUtil.getTenantId()) {
//         await message.prompt('请输入租户名称', t('common.reminder')).then(async ({ value }) => {
//           const res = await LoginApi.getTenantIdByName(value)
//           authUtil.setTenantId(res)
//         })
//       }
//     }
//     // 计算 redirectUri
//     // tricky: type、redirect需要先encode一次，否则钉钉回调会丢失。
//     // 配合 Login/SocialLogin.vue#getUrlValue() 使用
//     const redirectUri =
//       location.origin +
//       '/social-login?' +
//       encodeURIComponent(`type=${type}&redirect=${redirect.value || '/'}`)

//     // 进行跳转
//     const res = await LoginApi.socialAuthRedirect(type, encodeURIComponent(redirectUri))
//     window.location.href = res
//   }
// }
watch(
  () => currentRoute.value,
  (route: RouteLocationNormalizedLoaded) => {
    redirect.value = route?.query?.redirect as string
  },
  {
    immediate: true
  }
)
onMounted(() => {
  getLoginFormCache()
  // getTenantByWebsite()
})
</script>

<style lang="less" scoped>
:deep(.anticon) {
  &:hover {
    color: var(--el-color-primary) !important;
  }
}

.login-code {
  float: right;
  width: 100%;
  height: 38px;

  img {
    width: 100%;
    height: auto;
    max-width: 100px;
    vertical-align: middle;
    cursor: pointer;
  }
}

.custom-btn {
  background: #3370ff;
}

.custom-checkbox {
  :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
    color: #3370ff;
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: #3370ff;
    color: #3370ff;
  }

  :deep(.el-checkbox__inner:hover) {
    border-color: #3370ff;
  }
}
.back-btn {
  margin-top: 16px;
  font-size: 12px;
  color: #909399;
}
</style>
