<template>
    <Dialog v-model="dialogVisible" :title="titleName" width="80%" top="5vh">
        <div class="">

            <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
                <el-form-item label="员工姓名" prop="nickName">
                    <el-input v-model="queryParams.nickName" placeholder="请输入员工姓名" clearable @keyup.enter="handleQuery"
                        class="!w-240px" />
                </el-form-item>
                <el-form-item label="员工账号" prop="userName">
                    <el-input v-model="queryParams.userName" placeholder="请输入员工账号" clearable @keyup.enter="handleQuery"
                        class="!w-240px" />
                </el-form-item>
                <el-form-item label="时间" prop="dateRange">
                    <el-date-picker v-model="createTimeRange" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" class="!w-240px"
                        @change="hanlderTimeChange" />
                </el-form-item>


                <el-form-item>
                    <el-button @click="handleQuery" type="primary">
                        <Icon icon="ep:search" class="mr-5px" />
                        查询
                    </el-button>
                    <el-button @click="resetQuery">
                        <Icon icon="ep:refresh" class="mr-5px" />
                        重置
                    </el-button>
                </el-form-item>

            </el-form>


            <el-table v-loading="loading" :data="list" style="margin-top: 20px;">
                <el-table-column label="序号" align="center" width="100" type="index" />
                <el-table-column label="日期" align="center" width="120" prop="dateTime" />
                <el-table-column label="员工姓名" align="center" width="120" prop="nickName" />
                <el-table-column label="员工账号" align="center" prop="userName" />
                <el-table-column label="部门" align="center" width="280" prop="deptName" />
                <el-table-column label="当前位置" align="center" prop="address" />
                <el-table-column label="定位上传间隔" align="center" prop="diffTime" />

            </el-table>
            <!-- 分页 -->
            <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
                @pagination="getList" />
        </div>

        <template #footer>
            <el-button type="primary" @click="dialogVisible = false">关闭</el-button>
        </template>
    </Dialog>

    <!-- <ContentWrap class="h-1/1"> </ContentWrap> -->
</template>

<script lang="ts" setup>
    import AMapLoader from '@amap/amap-jsapi-loader'

    import {
        getUserTrackDetailPage,

    } from '@/api/location/index'

    const titleName = ref('定位详情')


    const message = useMessage() // 消息弹窗

    const dialogVisible = ref(false) // 弹窗的是否展示

    const createTimeRange = ref([])
    const loading = ref(true) // 列表的加载中
    const total = ref(0) // 列表的总页数
    const list = ref([]) // 列表的数据
    const queryParams = reactive({
        pageNo: 1,
        pageSize: 10,
        nickName: undefined,
        userName: undefined,
        startDate: undefined,
        endDate: undefined,

        sysUserId: undefined,
    })
    const queryFormRef = ref() // 搜索的表单

    const sysUserId = ref(null) // 系统用户id


    const getList = async () => {
        loading.value = true
        try {
            const res = await getUserTrackDetailPage(queryParams)
            console.log('getUserTrackSettingPage=', res)
            list.value = res.data.list
            total.value = res.data.total
        } finally {
            loading.value = false
        }
    }

    //修改时间
    const hanlderTimeChange = (val) => {
        if (!val || val.length == 0) {
            queryParams.startDate = undefined;
            queryParams.endDate = undefined;
        } else {
            queryParams.startDate = val[0];
            queryParams.endDate = val[1];
        }
        getList();
    }

    /** 搜索按钮操作 */
    const handleQuery = () => {
        queryParams.pageNo = 1
        getList()
    }

    /** 重置按钮操作 */
    const resetQuery = () => {

        queryFormRef.value.resetFields()
        createTimeRange.value = [];
        queryParams.startDate = undefined;
        queryParams.endDate = undefined;
        queryParams.sysUserId = sysUserId.value;
        handleQuery()
    }

    const open = async (id) => {
        sysUserId.value = id
        queryParams.sysUserId = sysUserId.value;

        getList()

        dialogVisible.value = true

    }




    defineExpose({ open }) // 提供 open 方法，用于打开弹窗

    /** 初始化 */
    onMounted(() => { })
</script>

<style lang="scss" scoped>



</style>