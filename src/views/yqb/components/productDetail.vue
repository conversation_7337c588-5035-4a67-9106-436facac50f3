<template>
  <div>
    <el-drawer :size="620" v-model="productDetailVisible2" destroy-on-close @close="handleClose">
      <template #title> 服务详情 </template>
      <div style="padding: 12px 0">
        <div style="display: flex; justify-content: space-between">
          <div style="display: flex; margin-bottom: 30px; ">
            <img :src="productDetail.logo" style="width: 62px; height: 62px; margin-right: 15px;border-radius: 16px;" />
            <div>
              <div class="card-title">
                {{ productDetail.name }}
              </div>
              <div class="center-align" style="margin-top: 8px">
                                <div class="center-align" v-if="productDetail.rebateRatioStr && productDetail.chargeRule==2"
                                    style="width: 74px;height:20px;position: relative;justify-content: flex-start;margin-right: 8px;">
                                    <img src="@/assets/images/business/rebate2.png"
                                        style="width: 74px;height:20px;position: absolute;top: 0;left: 0;" />
                                    <div class="center-align" style="position: absolute;z-index: 2;">


                                        <img style="width:14px;height:14px;margin-left: 3px;"
                                            src="@/assets/images/business/back.png" />
                                        <div style="font-size: 12px;color: #FFFFFF;line-height: 17px;margin-left: 3px;">
                                            {{ productDetail.rebateRatioStr }}
                                        </div>

                                        <img style="width: 4px;height: 8px;margin-left: 4px;"
                                            src="@/assets/images/business/arrow_right_s.png" />
                                    </div>
                                </div>
                                <div class="center-view" style="
                    height: 20px;
                    background: #ffeae3;
                    border-radius: 4px;
                    padding: 0 4px;
                    margin-right: 8px;
                  ">
                                    <img style="width: 16px; height: 16px; margin-right: 2px"
                                        src="@/assets/images/business/star_red.png" />

                                    <div style="font-size: 12px; color: #ff5757; line-height: 17px">
                                        {{ productDetail.score }}
                                    </div>
                                </div>
                                <div class="center-view" style="
                    height: 20px;
                    background: #e1f0ff;
                    border-radius: 4px;
                    padding: 0 4px;
                  ">
                                    <img style="width: 16px; height: 16px; margin-right: 2px"
                                        src="@/assets/images/business/category.png" />

                                    <div style="font-size: 12px; color: #3370ff; line-height: 17px">
                                        {{ productDetail.categoryName }}
                                    </div>
                                </div>
                            </div>

              <div class="center-align" style="
                  margin-top: 8px;
                  font-size: 12px;
                  color: #606266;
                  line-height: 17px;
                  max-width: 300px;
                  flex-wrap: wrap;
                ">
                <div class="center-align" v-for="(tagsItem, tagsIndex) in productDetail.tags.split('、')"
                  :key="tagsIndex">
                  {{ tagsItem }}
                  <div v-if="tagsIndex < productDetail.tags.split('、').length - 1" style="margin: 0 8px">|
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div style="flex-shrink: 0; margin-left: 30px">
            <div class="center-view recommend-button" @click="addRecommendOrderShow(productDetail, 1)">
              <img style="width: 16px; height: 16px; margin-right: 4px" src="@/assets/images/business/ask.png" />

              立即下单
            </div>
            <div class="center-view recommend-button2" @click="addRecommendOrderShow(productDetail, 3)">
              <img style="width: 16px; height: 16px; margin-right: 4px" src="@/assets/images/business/recommend2.png" />

              引荐
            </div>
          </div>
        </div>

        <div style="position: relative">
          <div style="
              position: absolute;
              border-bottom: 1px solid #eceded;
              left: -16px;
              top: 0;
              width: 600px;
            ">
          </div>
        </div>
        <div>
          <el-tabs v-model="detailActiveName" @tab-click="detailTabsChange">
            <el-tab-pane label="业务介绍" name="1">
                <template v-if="productDetail.serviceDescription && productDetail.serviceDescription.includes('oss-cn-hangzhou')">
                  <el-scrollbar style="height: calc(100vh - 324px); overflow: auto">
                    <div class="image-container" style="display: flex; flex-direction: column; gap: 16px; padding: 16px;">
                      <el-image 
                        v-for="(url, index) in productDetail.serviceDescription.split(',')" 
                        :key="index"
                        :src="url"
                        :preview-src-list="[url]"
                        fit="contain"
                        style="width: 100%;"
                      />
                    </div>
                  </el-scrollbar>
                </template>
                <template v-else>
                  <div class="center-view" style="margin-top: 5rem;flex-direction: column;">
                    <img src="@/assets/images/business/<EMAIL>" style="width: 12.5rem;height: 12.5rem;" />
                    <div style="font-size: 0.88rem;color: #909399;margin-top: 1rem;">暂无详情～</div>
                  </div>
                </template>
            </el-tab-pane>

            <el-tab-pane label="客户案例" name="2">
              <template v-if="productDetail.customerCase && productDetail.customerCase.includes('oss-cn-hangzhou')">
              <el-scrollbar style="height: calc(100vh - 324px); overflow: auto">
                <div class="image-container" style="display: flex; flex-direction: column; gap: 16px; padding: 16px;">
                  <el-image 
                    v-for="(url, index) in productDetail.customerCase.split(',')" 
                    :key="index"
                    :src="url"
                    :preview-src-list="[url]"
                    fit="contain"
                    style="width: 100%;"
                  />
                </div>
              </el-scrollbar>
              </template>
              <template v-else>
                <div class="center-view" style="margin-top: 5rem;flex-direction: column;">
                  <img src="@/assets/images/business/<EMAIL>" style="width: 12.5rem;height: 12.5rem;" />
                  <div style="font-size: 0.88rem;color: #909399;margin-top: 1rem;">暂无详情～</div>
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane label="客户评价" name="3">
              <div style="
                  font-weight: 500;
                  font-size: 18px;
                  color: #303133;
                  line-height: 25px;
                  margin-bottom: 24px;
                ">
                客户评价（{{ productDetail.totalCommentCount }}）
              </div>

              <!-- <div v-for="(item,index) in " style="">

                            </div> -->

              <template v-if="productCommentList && productCommentList.length > 0">
              <el-scrollbar ref="commentScrollbarRef" @scroll="handleCommentScroll"
                style="height: calc(100vh - 374px); overflow: auto">
                <div v-for="(item, index) in productCommentList" :key="index">
                  <div style="display: flex; justify-content: space-between">
                    <div style="font-size: 14px; color: #303133; line-height: 20px; max-width: 320px">
                      {{ item.content }}
                    </div>

                    <div style="font-size: 14px; color: #606266; line-height: 20px">
                      {{ item.customerEnterprise }} · {{ maskString(item.customerPeople) }}
                    </div>
                  </div>

                  <div style="display: flex; justify-content: space-between; margin-top: 12px">
                    <div class="center-align">
                      <img v-for="starItem in 5" :key="starItem" style="width: 20px; height: 20px; margin-right: 6px"
                        :src="starItem <= item.score ? starYellow : starGray" />

                      <div style="margin-left: 6px; font-size: 14px; color: #ed9512; line-height: 20px">
                        综合评分{{ item.score + '.0' }}
                      </div>
                    </div>

                    <div style="font-size: 14px; color: #909399; line-height: 20px">
                      {{ item.createTime }}
                    </div>
                  </div>

                  <div style="height: 1px; background: #eceded; margin-top: 25px; margin-bottom: 24px">
                  </div>
                </div>
              </el-scrollbar>
              </template>
              <template v-else>
                <div class="center-view" style="margin-top: 5rem;flex-direction: column;">
                  <img src="@/assets/images/business/<EMAIL>" style="width: 12.5rem;height: 12.5rem;" />
                  <div style="font-size: 0.88rem;color: #909399;margin-top: 1rem;">暂无评论～</div>
                </div>
              </template>
            </el-tab-pane>
            <!-- <el-tab-pane label="关于商家" name="4">
              <el-scrollbar style="height: calc(100vh - 324px); overflow: auto">
                <div class="temp-view" v-html="productDetail.aboutShop"> </div>
              </el-scrollbar>
            </el-tab-pane> -->
          </el-tabs>
        </div>
      </div>
    </el-drawer>


    <el-dialog v-model="recommendVisible" title="推荐客户" width="500">
      <el-form :model="recommendForm" :rules="rules" ref="recommendFormRef" label-width="100px">
        <el-form-item label="联系人选择" prop="type">
          <el-radio-group v-model="recommendForm.type">
            <el-radio :label="2">联系我</el-radio>
            <el-radio :label="3">联系客户</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="公司名称" prop="customerEnterprise">
          <el-input v-model="recommendForm.customerEnterprise" style="width: 300px" placeholder="请输入公司名称"></el-input>
        </el-form-item>
        <el-form-item label="联系人姓名" prop="customerPeople">
          <el-input v-model="recommendForm.customerPeople" style="width: 300px" placeholder="请输入联系人姓名"></el-input>
        </el-form-item>
        <el-form-item label="联系方式" prop="customerPhone">
          <el-input v-model="recommendForm.customerPhone" style="width: 300px" placeholder="请输入联系方式"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="recommendVisible = false">取消</el-button>
          <el-button type="primary" @click="handleRecommendSubmit(false)"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="payVisible" title="立即购买" width="500">
      <div class="center-align" style="margin-top: 24px">
        <div style="width: 70px; font-weight: 500; font-size: 14px; color: #303133; line-height: 20px">
          购买业务：
        </div>
        <div class="center-align">
          <img style="width: 38px; height: 38px; margin-right: 10px" :src="productDetail.logo" />

          <div>
            <div>{{ productDetail.name }}</div>
            <div class="center-align" style="
                margin-top: 8px;
                font-size: 12px;
                color: #606266;
                line-height: 17px;
                max-width: 260px;
                flex-wrap: wrap;
              ">
              <div class="center-align" v-for="(tagsItem, tagsIndex) in productDetail.tags.split('、')" :key="tagsIndex">
                {{ tagsItem }}
                <div v-if="tagsIndex < productDetail.tags.split('、').length - 1" style="margin: 0 8px">|
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="center-align" style="margin-top: 20px">
        <div style="width: 70px; font-weight: 500; font-size: 14px; color: #303133; line-height: 20px">
          业务价格：
        </div>
        <div class="center-align" style="font-size: 14px; color: #ff5757; line-height: 20px">
          ￥{{ productDetail.price }}
        </div>
      </div>

      <div class="center-align" style="margin-top: 20px">
        <div style="width: 70px; font-weight: 500; font-size: 14px; color: #303133; line-height: 20px">
          支付方式：
        </div>
        <div class="center-align" style="font-size: 14px; color: #ff5757; line-height: 20px">
          <el-radio-group v-model="payForm.payType" @change="payTypeChange">
            <el-radio :label="1">微信</el-radio>
            <el-radio :label="2">支付宝</el-radio>
          </el-radio-group>
        </div>
      </div>

      <div style="
          height: 200px;
          background: #f6f6f6;
          margin-top: 20px;
          border-radius: 4px;
          padding: 16px 32px;
        ">
        <div style="font-weight: 500; font-size: 18px; color: #303133; line-height: 25px">
          扫码购买
        </div>

        <div style="display: flex; margin-top: 20px">
          <img style="width: 120px; height: 120px; margin-right: 24px" :src="pqyCode" />

          <div>
            <div style="
                font-weight: 500;
                font-size: 14px;
                color: #303133;
                line-height: 20px;
                margin-top: 64px;
              ">
              支付金额
            </div>

            <div style="
                display: flex;
                align-items: flex-end;
                margin-top: 14px;
                font-weight: 500;
                font-size: 14px;
                color: #ff5757;
                line-height: 20px;
              ">
              <div>￥</div>
              <div style="font-size: 20px">{{ productDetail.price }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog v-model="paySuccessVisible" title="立即购买" width="500">
      <div class="center-view" style="padding: 83px 0 138px 0; flex-flow: column">
        <img style="width: 72px; height: 72px; margin-bottom: 16px" src="@/assets/images/business/success.png" />

        <div style="font-weight: 500; font-size: 18px; color: #303133; line-height: 25px">
          购买成功
        </div>

        <div style="font-size: 14px; color: #a2a3a5; line-height: 20px; margin-top: 8px">
          请稍等，商家后续会与您联系
        </div>
      </div>
    </el-dialog>



    <buyDialog :visible="buyDialogVisible" :productDetail="props.productDetail"
      @handleClose="buyDialogClose"></buyDialog>
  </div>
</template>

<script lang="ts" setup>
import buyDialog from './buyDialog.vue';
import { ref, onMounted, reactive, unref, nextTick } from 'vue'
import starYellow from '@/assets/images/business/star_yellow.png'
import starGray from '@/assets/images/business/star_gray.png'
import { throttle } from 'lodash'
import {
  addRecommendOrder,
  getProductPage,
  getProductDetail,
  getProductCategoryList,
  getProductCommentPage,
  postYqbOrderPay,
  getYqbOrderPayStatus,
  getYqbOrderPage
} from '@/api/common'
const message = useMessage() // 消息弹窗

const productDetailVisible2 = ref(false)

const props = defineProps({
  productDetailVisible: {
    type: Boolean,
    default: false
  },
  productDetail: {
    type: Object,
    default: () => ({})
  }
})

watch(
  () => props.productDetailVisible,
  (newPath, oldPath) => {
    console.log("productDetailVisible change", newPath)
    productDetailVisible2.value = newPath
    detailActiveName.value = '1'
  }
)

const emit = defineEmits(['handleClose'])

// 获取评论   getProductCommentPage
const productCommentParams = reactive({
  current: 1,
  size: 10,
  productName: ''
})

const detailActiveName = ref('1')

const productCommentList = ref([])
const commentScrollbarRef = ref() // 滚动条实例
const commentNoMore = ref(false) // 是否无更多数据

const handleCommentScroll = throttle((e) => {
  const { scrollTop, clientHeight, scrollHeight } = commentScrollbarRef.value?.wrapRef
  // 距离底部 50px 时触发
  if (scrollHeight - (scrollTop + clientHeight) < 50) {
    if (commentNoMore.value) return
    // console.log("handleRankingScroll")
    getProductComment()
  }
}, 200)

const getProductComment = async () => {
  productCommentParams.productName = props.productDetail.name

  const response = await getProductCommentPage(productCommentParams)
  // console.log("getProductCommentPage=", response)
  if (response.code === 0) {
    if (response.data.records.length === 0) {
      commentNoMore.value = true
      return
    }
    productCommentList.value = [...productCommentList.value, ...response.data.records]
    productCommentParams.current += 1
  }
}

const handleClose = () => {
  productDetailVisible2.value = false
  console.log('handleClose')
  emit('handleClose', false)
}

// 遷移相關方法
// 隐藏姓名
const maskString = (str: string) => {
  if (str.length <= 1) {
    // 如果字符串长度小于等于1，直接返回字符串重复该长度次数的'*'
    return '*'.repeat(str.length)
  }
  // 截取字符串除了最后一个字符之外的所有字符，然后替换为'*'
  return str.slice(0, -1).replace(/./g, '*') + str.slice(-1)
}

// 产品tab修改   detailTabsChange
const detailTabsChange = async (val: any) => {
  // console.log("detailTabsChange=", val)
  if (val.props.name == '3') {
    productCommentParams.current = 1
    productCommentList.value = []
    commentNoMore.value = false
    getProductComment()
  } else {
    if (commentScrollbarRef.value) {
      // 获取滚动容器的 DOM 元素
      commentScrollbarRef.value.scrollTo({ top: 0 }) // 平滑滚动
    }
  }
}



//
const recommendVisible = ref(false)

const recommendForm = reactive({
  productId: null,
  type: 3,
  customerEnterprise: '',
  customerPhone: '',
  customerPeople: '',
  buyNum:null
})
// 定义表单引用
const recommendFormRef = ref(null)

const payForm = reactive({
  id: null,
  payType: 1
})

const handleRecommendSubmit = async (needPay: boolean) => {
  if (!recommendFormRef) return
  await recommendFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      const response = await addRecommendOrder(recommendForm)

      if (response.code === 0) {
        recommendVisible.value = false
        // console.log("addRecommendOrder res=", response)
        message.success('提交成功')

        // if (response.data.id) {
        //   payForm.id = response.data.id

        //   getPayCode(1,1)

        //   // const postYqbOrderPay = await postYqbOrderPay({
        //   //   id: response.data.id,
        //   //   payType:1,
        //   // })
        // }
      } else {
        message.error(response.msg)
      }
    } else {
    }
  })
}

const pqyCode = ref('')
const mchOrderNo = ref('')

// 获取支付二维码
const getPayCode = async (payType: number, type: number) => {
  payForm.payType = payType
  const response = await postYqbOrderPay(payForm)

  if (response.code === 0) {
    console.log('getPayCode res=', response)

    if (type == 2) {
      message.success('切换成功')
    }

    mchOrderNo.value = response.data.mchOrderNo
    pqyCode.value = response.data.url
    payVisible.value = true
  } else {
    message.error(response.msg)
  }
}

const payTypeChange = async (val: any) => {
  console.log('payTypeChange=', val)
  getPayCode(val, 2)
}

const buyDialogVisible = ref(false)
const buyDialogClose = () => {
  buyDialogVisible.value = false
}

// 引荐
const addRecommendOrderShow = async (val: any, type: any) => {
  recommendForm.productId = val.id
  recommendForm.type = type
  recommendForm.customerEnterprise = ''
  recommendForm.customerPhone = ''
  recommendForm.customerPeople = ''
  recommendForm.buyNum = 1

  if (type == 1) {
    buyDialogVisible.value = true
    console.log('buyDialogVisible=', buyDialogVisible.value)
    return
    const response = await addRecommendOrder(recommendForm)

    if (response.code === 0) {
      // recommendVisible.value = false
      // console.log("addRecommendOrder res=", response)
      //  message.success('提交成功')

      if (props.productDetail.chargeRule == 2) {
        if (response.data.id) {
          payForm.id = response.data.id
          paySuccessVisible.value = true
          // getPayCode(1, 1)

          // const postYqbOrderPay = await postYqbOrderPay({
          //   id: response.data.id,
          //   payType:1,
          // })
        }
      } else {
        paySuccessVisible.value = true
      }
    } else {
      message.error(response.msg)
    }
  } else {
    recommendVisible.value = true
  }

  // recommendVisible.value = true
}






const rules = reactive<FormRules<any>>({
  customerEnterprise: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
  customerPeople: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  customerPhone: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],
  type: [
    {
      required: true,
      message: '请选择联系类型',
      trigger: 'change'
    }
  ],
})

const payVisible = ref(false)

const paySuccessVisible = ref(false)

// 轮询相关变量
let pollTimer = null
const POLL_INTERVAL = 2000 // 2秒间隔

// 模拟 API 调用（替换为你的真实接口）
const fetchPaymentStatus = async () => {
  try {
    const response = await getYqbOrderPayStatus({ mchOrderNo: mchOrderNo.value }) // 替换为你的接口调用
    if (response.code === 0) {
      if (response.data && response.data.state === 2) {
        // 成功条件：停止轮询
        stopPolling()
        payVisible.value = false
        paySuccessVisible.value = true
      }
    }
    return response
  } catch (error) {
    console.error('轮询请求失败:', error)
    // 失败时可根据需求处理（如重试机制）
    throw error
  }
}

// 启动轮询
const startPolling = () => {
  // 先清除可能存在的旧定时器
  stopPolling()

  // 立即执行第一次请求
  fetchPaymentStatus()

  // 设置定时器
  pollTimer = setInterval(async () => {
    if (!payVisible.value) {
      stopPolling()
      return
    }

    await fetchPaymentStatus()
  }, POLL_INTERVAL)
}

// 停止轮询
const stopPolling = () => {
  if (pollTimer) {
    clearInterval(pollTimer)
    pollTimer = null

    console.log('轮询已停止')
  }
}

// 监听状态变化
watchEffect(() => {
  if (payVisible.value) {
    console.log('开始轮询')
    setTimeout(() => {
      startPolling()
    }, 2000)
  } else {
    stopPolling()
  }
})

// 组件卸载时自动清理
onUnmounted(stopPolling)






</script>

<style scoped lang="less">
:deep(.el-card__body) {
  padding: 0 !important;
}

:deep(.el-drawer__header) {
  margin-bottom: 0px !important;
  justify-content: space-between;
}

:deep(.el-drawer__header > :first-child) {
  flex: 0 1 auto !important;
}


.recommend-button {
  width: 100px;
  height: 32px;
  background: #3370ff;
  border-radius: 4px;
  font-size: 12px;
  color: #ffffff;
  line-height: 17px;
  cursor: pointer;
}

.recommend-button2 {
  width: 100px;
  height: 32px;
  background: #e1f0ff;
  border-radius: 4px;
  font-size: 12px;
  color: #3370ff;
  line-height: 17px;
  margin-top: 8px;
  cursor: pointer;
}

:deep(.temp-view) {
  img {
    max-width: 100%;
    /* 图片宽度不超过父容器 */
    height: auto;
    /* 保持图片比例 */
  }

  image {
    max-width: 100%;
    /* 图片宽度不超过父容器 */
    height: auto;
    /* 保持图片比例 */
  }

  p,
  span,
  div {
    word-wrap: break-word;
    /* 防止文本内容溢出 */
    overflow-wrap: break-word;
    /* 同上 */
  }
}

:deep(.el-tabs__nav-wrap:after){
  background-color:transparent!important;
}
</style>
