<template>
    <div class="container center-view">


        <div style="width:1000px;background: #F8F9F9;">
            <div style="height: 304px;position: relative;">
                <img src="@/assets/images/business/mask_bg.png"
                    style="width: 100%;height: 100%;position: absolute;top: 0;left: 0;z-index: 0;" />

                <el-carousel indicator-position="none" style="height: 304px;position: relative;">
                    <el-carousel-item v-for="item in entDetail.bannerPic" :key="item" style="height: 304px;">
                        <div class="center-view" style="height: 304px;">
                            <img style="width:400px;height:304px;display:block" :src="item.banner" />
                        </div>
                    </el-carousel-item>
                </el-carousel>
            </div>

            <div style="background: #454B63;padding:32px;">
                <div style="display:flex;justify-content: space-between">
                    <div style="display: flex;">
                        <img :src="entDetail.enterpriseLogo"
                            style="width: 82px;height:82px;margin-right: 24px;border-radius: 16px;" />
                        <div>
                            <div
                                style="margin-top: 7px;margin-bottom: 12px;font-weight: 500;font-size: 22px;color: #FFFFFF;line-height: 30px;">
                                {{ entDetail.enterpriseName }}
                            </div>

                            <div v-if="entDetail.platformTags" style="display: flex;flex-wrap: wrap;">
                                <div v-for="(tagItem, tagIndex) in entDetail.platformTags.split(',')" :key="tagIndex"
                                    class="platform-tags2 center-view">
                                    {{ tagItem }}
                                </div>
                            </div>
                            <div v-if="entDetail.tags" style="display: flex;">
                                <div v-for="(tagItem, tagIndex) in entDetail.tags.split(',')" :key="tagIndex"
                                    class="tags2 center-view">
                                    {{ tagItem }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="flex-shrink: 0;margin-left: 20px;display: flex;margin-top: 10px;">
                        <img v-for="starItem in 5" :key="starItem" style="width: 20px; height: 20px; margin-right: 6px"
                            :src="starItem <= Number(entDetail.score) ? starYellow : starGray" />
                        <div style="
                        margin-left: 6px;
                        font-size: 14px;
                        color: #ed9512;
                        line-height: 20px;
                      ">
                            综合评分{{ entDetail.score }}
                        </div>
                    </div>

                </div>

                <div style="height: 1px;background: #818697;margin-top: 32px;"></div>

                <div class="center-align" style="margin-top: 24px;justify-content: space-between;">

                    <div style="color: #DCDFE6;line-height: 22px;">
                        {{ entDetail.address }}
                    </div>

                    <div v-if="entDetail.coordinate" @click="navigation()" class="center-align"
                        style="color: #AEB2BE;line-height: 22px;flex-shrink: 0;margin-left: 24px;cursor: pointer;">
                        <!--距离方面的代码  -->
                        <div v-if="entDetail.distance">距离你{{ entDetail.distance }}km</div>
                        <img style="width:20px;height:20px;margin-left: 10px;"
                            src="@/assets/images/business/navigation.png" />

                    </div>

                </div>
                <div style="margin-top: 32px;font-size: 18px;color: #FFFFFF;line-height: 25px;">
                    公司介绍
                </div>
                <div style="margin-top: 24px;display: flex;">

                    <img style="width:280px;height: 136px;margin-right: 32px;flex-shrink: 0;" :src="entDetail.pic" />

                    <div style="font-size: 14px;color: #DCDFE6;line-height: 24px;">
                        {{ entDetail.introduce }}
                    </div>

                </div>



            </div>


            <div style="padding:16px 24px;">

                <div v-if="entDetail.productList.length > 0"
                    style="padding:24px 24px 1px;background: #FFFFFF;border-radius: 8px;">

                    <div style="font-weight: 500;font-size: 20px;color: #303133;line-height: 28px;margin-bottom: 18px;">
                        产品服务（{{ entDetail.productList.length }}）
                    </div>

                    <div v-for="(productItem, productIndex) in entDetail.productList" :key="productIndex"
                        @click="productDetailShow(productItem)"
                        style="margin-bottom: 24px;margin-top: 6px;display: flex;justify-content: space-between;cursor: pointer;">

                        <div style="display: flex;">
                            <img style="width:62px;height:62px;flex-shrink: 0;margin-right: 15px;border-radius: 16px;"
                                :src="productItem.logo" />

                            <div style="margin-top: 1px;">
                                <div style="font-size: 18px;color: #303133;line-height: 25px;">
                                    {{ productItem.productName }}
                                </div>

                                <div class="center-align" style="margin-top: 8px">
                                    <div class="center-align"
                                        v-if="productItem.rebateRatioStr && productItem.chargeRule == 2"
                                        style="width: 74px;height:20px;position: relative;justify-content: flex-start;margin-right: 8px;">
                                        <img src="@/assets/images/business/rebate2.png"
                                            style="width: 74px;height:20px;position: absolute;top: 0;left: 0;" />
                                        <div class="center-align" style="position: absolute;z-index: 2;">


                                            <img style="width:14px;height:14px;margin-left: 3px;"
                                                src="@/assets/images/business/back.png" />
                                            <div
                                                style="font-size: 12px;color: #FFFFFF;line-height: 17px;margin-left: 3px;">
                                                {{ productItem.rebateRatioStr }}
                                            </div>

                                            <img style="width: 4px;height: 8px;margin-left: 4px;"
                                                src="@/assets/images/business/arrow_right_s.png" />
                                        </div>
                                    </div>
                                    <div class="center-view"
                                        style="height: 20px;background: #ffeae3;border-radius: 4px;padding: 0 4px;margin-right: 8px;">
                                        <img style="width: 16px; height: 16px; margin-right: 2px"
                                            src="@/assets/images/business/star_red.png" />

                                        <div style="font-size: 12px; color: #ff5757; line-height: 17px">
                                            {{ productItem.score }}
                                        </div>
                                    </div>
                                    <div class="center-view"
                                        style="height: 20px;background: #e1f0ff;border-radius: 4px;padding: 0 4px;">
                                        <img style="width: 16px; height: 16px; margin-right: 2px"
                                            src="@/assets/images/business/category.png" />

                                        <div style="font-size: 12px; color: #3370ff; line-height: 17px">
                                            {{ productItem.categoryName }}
                                        </div>
                                    </div>

                                </div>
                                <div class="center-align" v-if="productItem.tags"
                                    style="margin-top: 8px;font-size: 12px;color: #606266;line-height: 17px; max-width: 300px;flex-wrap: wrap;">
                                    <div class="center-align" v-for="(tagsItem, tagsIndex) in productItem.tags.split(
                        '、'
                    )" :key="tagsIndex">
                                        {{ tagsItem }}
                                        <div v-if="tagsIndex < productItem.tags.split('、').length - 1"
                                            style="margin: 0 8px">|
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <img style="width: 24px;height:24px;" src="@/assets/images/business/arrow_right_gray.png" />




                    </div>



                </div>




                <div v-if="recommendedEntList.length > 0"
                    style="padding:24px 24px 1px;background: #FFFFFF;border-radius: 8px;margin-top: 16px;">


                    <div style="font-weight: 500;font-size: 20px;color: #303133;line-height: 28px;margin-bottom: 18px;">
                        推荐商家
                    </div>

                    <div class="ent-item" v-for="(item, index) in recommendedEntList" :key="index" @click="toEnt(item)">
                        <div class="" style="display:flex;">
                            <img :src="item.enterpriseLogo"
                                style="width: 104px; height: 104px; margin-right: 14px;flex-shrink: 0;border-radius: 16px;" />

                            <div style="flex:1;">
                                <div class="center-align" style="justify-content: space-between;">
                                    <div class="title textover">
                                        {{ item.enterpriseName }}
                                    </div>
                                    <div>
                                        <div style="flex-shrink: 0;margin-left: 20px;display: flex;">
                                            <img v-for="starItem in 5" :key="starItem"
                                                style="width: 20px; height: 20px; margin-right: 6px"
                                                :src="starItem <= Number(item.score) ? starYellow : starGray" />
                                            <div style="
                        margin-left: 6px;
                        font-size: 14px;
                        color: #ed9512;
                        line-height: 20px;
                      ">
                                                综合评分{{ item.score }}
                                            </div>
                                        </div>

                                    </div>


                                </div>
                                <div v-if="item.address" class="address textover">
                                    {{ item.address }}
                                </div>

                                <div v-if="item.platformTags" style="display: flex;margin-top: 7px;">
                                    <div v-for="(tagItem, tagIndex) in item.platformTags.split(',').slice(0, 7)"
                                        :key="tagIndex" class="platform-tags center-view">
                                        {{ tagItem }}
                                    </div>
                                    <div v-if="item.platformTags.split(',').length > 7" style="width: 22px;"
                                        class="platform-tags center-view">
                                        ...
                                    </div>
                                </div>
                                <div v-if="!item.platformTags && item.tags" style="display: flex;margin-top: 7px;">
                                    <div v-for="(tagItem, tagIndex) in item.tags.split(',')" :key="tagIndex"
                                        class="tags center-view">
                                        {{ tagItem }}
                                    </div>
                                </div>

                                <div style="display: flex;margin-top: 8px;">

                                    <div class="recent center-view">
                                        <img style="width: 16px; height: 16px; margin-right: 1px"
                                            src="@/assets/images/business/recent.png" />

                                        近期服务{{ item.totalOrderCount }}

                                    </div>

                                    <div v-if="item.platformTags && item.tags" style="display: flex;">
                                        <div v-for="(tagItem, tagIndex) in item.tags.split(',')" :key="tagIndex"
                                            class="tags center-view">
                                            {{ tagItem }}
                                        </div>
                                    </div>

                                </div>


                            </div>

                        </div>
                        <div class="center-align" style="margin-top: 14px">
                            <div style="width:118px;"></div>

                            <div style="flex:1;height: 1px;background: #ECEDED;"></div>

                        </div>
                        <div v-for="(productItem, productIndex) in item.productList" :key="productIndex"
                            class="product-item center-align">
                            <div
                                style="width:104px;margin-right: 14px;display: flex;justify-content: flex-end;flex-shrink: 0;">
                                <div class="center-align"
                                    v-if="productItem.rebateRatioStr && productItem.chargeRule == 2"
                                    style="width: 74px;height:20px;position: relative;justify-content: flex-end;">
                                    <img src="@/assets/images/business/rebate.png"
                                        style="width: 74px;height:20px;position: absolute;top: 0;left: 0;" />
                                    <div class="center-align" style="position: absolute;z-index: 2;">
                                        <div style="font-size: 12px;color: #FFFFFF;line-height: 17px;">
                                            {{ productItem.rebateRatioStr }}
                                        </div>
                                        <img style="width: 4px;height: 8px;margin-left: 2px;margin-right: 5px;"
                                            src="@/assets/images/business/arrow_right_s.png" />
                                        <img style="width:14px;height:14px;margin-right: 3px;"
                                            src="@/assets/images/business/back.png" />
                                    </div>
                                </div>
                            </div>
                            <div class="center-align" style="flex:1;">
                                <img style="width:20px;height:20px;margin-right: 8px;"
                                    src="@/assets/images/business/product.png" />
                                <div class="textover"
                                    style="max-width: 500px;font-size: 14px;color: #303133;line-height: 20px;">
                                    {{ productItem.price }}元
                                    {{ productItem.productName }}
                                </div>
                            </div>



                        </div>


                    </div>

                </div>

            </div>
        </div>

        <productDetailDrawer :productDetailVisible="productDetailVisible" :product-detail="productDetail"
            @handleClose="productDetailClose"></productDetailDrawer>

    </div>
</template>

<script lang="ts" setup>
import productDetailDrawer from './components/productDetail.vue';
import starYellow from '@/assets/images/business/star_yellow.png';
import starGray from '@/assets/images/business/star_gray.png';
import allClassify from '@/assets/images/business/all_classify.png';
import { ref, onMounted, reactive, unref, nextTick } from 'vue';
import {
    getProductDetail,
    getYqbProductEntDetail,
    getYqbProductEntPage,

} from '@/api/common'
import { throttle } from 'lodash';
import router from '@/router';


import { useRoute } from 'vue-router';
const route = useRoute();

console.log("route.query=", route.query)

const enterpriseId = route.query.enterpriseId;
const categoryId = route.query.categoryId;

console.log("categoryId=", categoryId)
console.log("enterpriseId=", enterpriseId)

const entDetail = ref({
    bannerPic: [],
    productList: []
});

const getEntDetail = async () => {
    const response = await getYqbProductEntDetail({
        enterpriseId: enterpriseId,
        categoryId: categoryId,
        lo: localStorage.getItem('lo') || '',
        la: localStorage.getItem('la') || '',
    });


    console.log("entDetail=", response)
    if (response.code === 0) {
        entDetail.value = response.data;

    }
};

getEntDetail()


const recommendedEntList = ref([]);

const getRecommendedEntList = async () => {
    const response = await getYqbProductEntPage({
        current: 1,
        size: 15,
        sortType: 1,
        enterpriseId: enterpriseId,
        categoryId: categoryId,
    });
    console.log("分页列表=", response)
    if (response.code === 0) {
        recommendedEntList.value = response.data.records;

    }
};

getRecommendedEntList()


const productDetailVisible = ref(false);
const productDetail = ref({});

const productDetailShow = async (val: any) => {
    const response = await getProductDetail({ id: val.productId });
    // console.log("productDetail=", response)
    if (response.code === 0) {
        productDetail.value = response.data;
        productDetailVisible.value = true;
        console.log('productDetailShow', productDetail.value);
    }
};
const productDetailClose = async (val: any) => {
    console.log('productDetailClose', val);
    productDetailVisible.value = false;
    console.log('productDetailClose', productDetail.value);

};


// 前往商家详情页面 

const toEnt = (val: any) => {
    let query = {
        enterpriseId: val.enterpriseId,
        categoryId: undefined
    }

    if (categoryId) {
        query.categoryId = queryParams.categoryId
    }

    router.push({ name: 'yabBusiness', query: query });
};





const navigation = () => {
    // 假设你已经获取到了当前的经纬度
    const startLat = localStorage.getItem('la') || ''; // 替换为当前经纬度的纬度
    const startLng = localStorage.getItem('lo') || ''; // 替换为当前经纬度的经度

    // 目标位置经纬度
    const [endLat, endLng] = entDetail.value.coordinate.split(',');

    if (startLat && startLng) {
        // 如果你想从当前经纬度导航到目标位置，可以构造如下URL
        const navigationUrl = `https://uri.amap.com/navigation?from=${startLng},${startLat},当前位置&to=${endLng},${endLat},${entDetail.value.address}&src=mypage&coordinate=gaode`;

        // 打开高德地图进行导航
        window.open(navigationUrl, '_blank');
    } else {
        const amapUrl = `https://uri.amap.com/marker?position=${endLng},${endLat}&name=${entDetail.value.address}&src=mypage&coordinate=gaode&callnative=0`;
        window.open(amapUrl, '_blank');
    }
};




</script>

<style scoped lang="less">
.tags2 {
    width: 60px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #DCDFE6;
    font-size: 12px;
    color: #DCDFE6;
    line-height: 17px;
    margin-right: 8px;
    flex-shrink: 0;
}

.platform-tags2 {
    width: 60px;
    height: 20px;
    background: #F4DFB5;
    border-radius: 4px;
    font-size: 12px;
    color: #9B7638;
    line-height: 17px;
    margin-right: 8px;
    flex-shrink: 0;
    margin-bottom: 12px;
}


.ent-item {


    cursor: pointer;

    margin-bottom: 30px;


    .title {
        font-weight: 500;
        font-size: 16px;
        color: #303133;
        line-height: 22px;
        max-width: 528px;
        margin-top: 2px;
    }

    .address {
        font-size: 12px;
        color: #909399;
        line-height: 17px;
        margin-top: 6px;
        max-width: 528px;
    }

    .tags {
        width: 60px;
        height: 20px;
        border-radius: 4px;
        border: 1px solid #DCDFE6;
        font-size: 12px;
        color: #909399;
        line-height: 17px;
        margin-right: 8px;
        flex-shrink: 0;
    }

    .platform-tags {
        width: 60px;
        height: 20px;
        background: #F4DFB5;
        border-radius: 4px;
        font-size: 12px;
        color: #9B7638;
        line-height: 17px;
        margin-right: 8px;
        flex-shrink: 0;
    }

    .recent {
        flex-shrink: 0;
        padding: 0 6px;
        height: 20px;
        background: #FFEAE3;
        border-radius: 4px;
        font-size: 12px;
        color: #FF5757;
        line-height: 17px;
        margin-right: 8px;
    }

    .product-item {
        margin-top: 12px;
    }
}
</style>
