<template>
  <div class="fileDiv" v-if="props.fileList.length > 0">
    <p>附件：</p>
    <div class="contentDiv" v-for="(item, index) in props.fileList" :key="index">
      <div class="forDiv" @click="openRead(item)">{{ item.name }}</div>
    </div>
  </div>
  <readFile ref="readFileRef" />
</template>
<script lang="ts" setup>
import readFile from './readFile.vue'
const props = defineProps({
  fileList: {
    type: Array,
    default: []
  },
  detailObj: {
    type: Object,
    default: {}
  },
  isDown: {
    type: Boolean,
    default: false
  }
})
const readFileRef = ref()
const openRead = (row) => {
  readFileRef.value.open(row, props.isDown, '', props.detailObj)
}
</script>
<style lang="less" scoped>
.fileDiv {
  // border-top: 1px solid #c7c7cc;
  padding: 20px 0;
  // width: 30%;
  margin-top: 20px;
  position: relative;
}
.fileDiv::before{
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  content:'';
  background: #c7c7cc;
  height: 1px;
  width: 200px;
}

p {
  margin: 0;
  margin-bottom: 15px;
}

.contentDiv {
  margin: 0 0 8px 50px;
  display: flex;
  align-items: center;
}

.forDiv {
  padding: 12px;
  border-radius: 5px;
  border: 1px solid #eceded;
  color: #303133;
  font-size: 14px;
  cursor: pointer;
}

.forDiv:hover {
  color: #3370FF;
}

.downSpan {
  color: #3370FF;
  font-size: 12px;
  display: inline-block;
  margin-left: 10px;
  cursor: pointer;
}
</style>