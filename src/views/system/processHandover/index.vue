<template>
  <div class="rowBox" :class="{ isCol: isCollapse, 'isCBox-else': windowWidth < 768 }">
    <div class="rTop">
      <span
        :class="tableIndex == i ? 'dx' : ''"
        v-for="(item, i) in topData"
        :key="i"
        @click="selectTableIndex(i)"
        >{{ item.text }}</span
      >
      <!-- <span>审批流程交接</span> -->
    </div>
  </div>

  <ContentWrap class="center">
    <!-- 审批流程交接 -->
    <div v-if="tableIndex == 0">
      <el-button type="primary" class="riButton" @click="newHandover()"> 发起新交接 </el-button>
      <el-table v-loading="loading" :data="approvalData">
        <el-table-column align="left" label="被交接人" prop="companyName">
          <template #default="scope">
            <div class="taUser">
              <div class="taUserLeft">{{
                scope.row.applyUserName
                  ? scope.row.applyUserName.length >= 3
                    ? scope.row.applyUserName.substring(1, 3)
                    : scope.row.applyUserName
                  : '-'
              }}</div>
              <div class="taUser1">
                <div>{{ scope.row.applyUserName }}</div>
                <!-- <div @click="jump">
                  <span>{{ scope.row.deptNames }}</span>
                  <el-icon style="vertical-align: middle"><ArrowRight /></el-icon>
                </div> -->
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="left" label="交接人" prop="handoverUserName">
          <template #default="scope">
            <div class="taUser">
              <div class="taUserLeft">{{
                scope.row.handoverUserName
                  ? scope.row.handoverUserName.length >= 3
                    ? scope.row.handoverUserName.substring(1, 3)
                    : scope.row.handoverUserName
                  : '-'
              }}</div>
              <div class="taUser1">
                <div>{{ scope.row.handoverUserName }}</div>
                <!-- <div @click="jump">
                  <span>{{ scope.row.deptNames }}</span>
                  <el-icon style="vertical-align: middle"><ArrowRight /></el-icon>
                </div> -->
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="left" label="交接审批模板" prop="handoverModels" />
        <!-- <el-table-column align="left" label="操作人" prop="instanceId" width="240" /> -->
        <el-table-column
          align="left"
          label="操作时间"
          prop="createTime"
          :formatter="dateFormatter"
        />
        <el-table-column align="left" label="交接状态" prop="status">
          <template #default="scope">
            <div
              :style="{
                color:
                  scope.row.status == 0
                    ? '#53D991'
                    : scope.row.status == 1
                    ? 'red'
                    : scope.row.status == 2
                    ? 'red'
                    : scope.row.status == 3
                    ? '#EAB059'
                    : ''
              }"
              >{{
                scope.row.status == 0
                  ? '已完成'
                  : scope.row.status == 1
                  ? '流程交接失败'
                  : scope.row.status == 2
                  ? '任务转交失败'
                  : scope.row.status == 3
                  ? '处理中'
                  : '-'
              }}</div
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNo"
        :total="total"
        @pagination="getList"
      />
    </div>
    <!-- 待处理审批单交接 -->
    <div v-show="tableIndex != 0">
      <handoverApprovalForms />
    </div>
  </ContentWrap>

  <Dialog v-model="dialogVisible" title="发起新交接">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      label-width="80px"
      label-position="top"
    >
      <!-- <el-form-item label="被交接人">
        <div style="max-width: 350px">
          <el-button type="primary" size="default" round @click="$refs.orgPicker.show()"
            >请选择被交接人</el-button
          >
          <div style="margin-top: 5px">
            <el-tag
              size="small"
              style="margin: 5px"
              closable
              v-for="(dept, i) in Uservalue"
              @close="delDept(i, 1)"
              >{{ dept.name }}</el-tag
            >
          </div>
        </div>
      </el-form-item> -->

      <el-form-item label="被交接人">
        <el-select
          v-model="currentApprover"
          placeholder="请选择类型"
          class="!w-240px JuF"
          @change="secetApprover"
        >
          <el-option
            v-for="item in resignationEmployed"
            :key="item.id"
            :label="item.textName"
            :value="item.textName"
          />
        </el-select>
        <div @click="inputFocus" class="deptClass">
          <el-input readonly placeholder="请选择" class="!w-240px" />
          <!-- 在职选择人员 -->
          <org-picker
            title="请选择"
            ref="resignation"
            :selected="Uservalue"
            @ok="selected"
            type="user"
            v-if="quitFlag == '0'"
          />
          <!-- 离职选择人员 -->
          <quitOrgPicker
            v-if="quitFlag == '1'"
            :selected="Uservalue"
            @ok="selected"
            type="user"
            ref="resignation"
          />
          <div v-if="Uservalue.length > 0" class="tagItem" ref="tagItemRef">
            <el-tag type="info" v-for="(item, i) in Uservalue" :key="i" @close="closeTag(item)">{{
              item.name
            }}</el-tag>
          </div>
        </div>
      </el-form-item>

      <!-- <el-form-item label="交接人">
        <div style="max-width: 350px">
          <el-button type="primary" size="default" round @click="$refs.orgPicker1.show()"
            >请选择交接人</el-button
          >
          <div style="margin-top: 5px">
            <el-tag
              size="small"
              style="margin: 5px"
              closable
              v-for="(dept, i) in Uservalue1"
              @close="delDept(i, 2)"
              >{{ dept.name }}</el-tag
            >
          </div>
        </div>
      </el-form-item> -->

      <el-form-item label="交接人">
        <div @click="inputFocus1" class="deptClass">
          <el-input readonly placeholder="请选择" class="!w-240px" />
          <org-picker
            title="请选择"
            ref="resignation1"
            :selected="Uservalue1"
            @ok="selected1"
            type="user"
          />
          <div v-if="Uservalue1.length > 0" class="tagItem" ref="tagItemRef">
            <el-tag type="info" v-for="(item, i) in Uservalue1" :key="i" @close="closeTag(item)">{{
              item.name
            }}</el-tag>
          </div>
        </div>
      </el-form-item>

      <!-- filterable -->
      <!-- multiple -->
      <el-form-item label="生效审批模板">
        <el-select
          class="!w-240px"
          v-model="formData.templateValue"
          placeholder="请选择"
          clearable
          disabled
        >
          <el-option
            v-for="item in approvalTemplateList"
            :key="item.id"
            :label="item.text"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <span style="color: #a2a3a5"> 选中的审批模板中，“被交接人”的审批节点替换为“交接人” </span>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
  <!-- <org-picker ref="orgPicker" type="user" :selected="Uservalue" @ok="selected" /> -->
  <!-- <org-picker ref="orgPicker1" type="user" :selected="Uservalue1" @ok="selected1" /> -->
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { getUserProfile } from '@/api/system/user/profile'
import { Plus, ArrowRight } from '@element-plus/icons-vue'
import { tr } from 'element-plus/es/locale'
import { useAppStore } from '@/store/modules/app'
import * as handoverApi from '@/api/system/processHandover'
import { dateFormatter } from '@/utils/formatTime'
import OrgPicker from '@/components/common/OrgPicker.vue'
import handoverApprovalForms from '@/views/system/processHandover/handoverApprovalForms.vue'
import quitOrgPicker from '@/components/common/quitOrgPicker.vue'

import dayjs from 'dayjs'
defineOptions({ name: 'orgstructure' })
import { factory } from 'typescript'
const isMobile = computed(() => window.screen.width < 450)
const userMessage = ref([]) // 消息弹窗
const Uservalue = ref([])
const Uservalue1 = ref([])
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const total = ref(0) // 列表的总页数
const loading = ref(false)
const formLoading = ref(false)
const currentApprover = ref('在职')
const resignationEmployed = ref([
  {
    id: 1,
    textName: '在职'
  },
  {
    id: 2,
    textName: '离职'
  }
])
const topData = [
  {
    text: '审批流程交接'
  },

  {
    text: '待处理审批单交接'
  }
]
const approvalTemplateList = [
  {
    id: '1',
    text: '全部'
  }
]

const approvalData = ref([])
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const formData = ref({
  templateValue: '1'
})
const tableIndex = ref(0)
const dialogVisible = ref(false)
const selectTableIndex = (i) => {
  tableIndex.value = i
}
// 表格数据
const getList = async () => {
  loading.value = true
  const data = await handoverApi.processHandoverData(queryParams)
  loading.value = false
  console.log(data)
  approvalData.value = data.list
  total.value = data.total
}

// 跳转到待处理审批单交接
const jump = () => {
  tableIndex.value = 1
}
const quitFlag = ref('0')
const secetApprover = (v) => {
  if (v == '在职') {
    quitFlag.value = '0'
  } else {
    quitFlag.value = '1'
  }
}
const resignation = ref()
const inputFocus = (e) => {
  if (e.target.tagName.toLowerCase() === 'input') {
    return
  }
  resignation.value.show()
}
const resignation1 = ref()
const inputFocus1 = (e) => {
  if (e.target.tagName.toLowerCase() === 'input') {
    return
  }
  resignation1.value.show()
}
// 发起新交接
const newHandover = () => {
  Uservalue.value = []
  Uservalue1.value = []
  dialogVisible.value = true
}

// 选择被交接人
const selected = (va) => {
  console.log(va.name)
  Uservalue.value = va.map((item, i) => {
    return {
      name: item.name,
      id: item.id,
      type: item.type || ''
    }
  })
}
// 选择交接人
const selected1 = (va) => {
  Uservalue1.value = va.map((item, i) => {
    return {
      name: item.name,
      id: item.id,
      type: item.type || ''
    }
  })
}
const delDept = (i, n) => {
  if (n == 1) {
    Uservalue.value.splice(i, 1)
  } else if (n == 2) {
    Uservalue1.value.splice(i, 1)
  }
}
const submitForm = async () => {
  if (Uservalue.value.length <= 0) return message.warning('请选择被交接人')
  if (Uservalue1.value.length <= 0) return message.warning('请选择交接人')
  formLoading.value = true
  await handoverApi.createHandover({
    applyUserId: Uservalue.value[0].id,
    handoverUserId: Uservalue1.value[0].id,
    handoverModels: '全部',
    applyUserStatus: quitFlag.value
  })
  formLoading.value = false
  dialogVisible.value = false
  message.success('操作成功')
  getList()
}

// 获取用户个人登录信息
// const getUserInfo = async () => {
//   const users = await getUserProfile()
//   userInfo.value = users
// }

// 监听折叠面板
const appStore = useAppStore()
const collapse = computed(() => appStore.getCollapse)
const isCollapse = computed(() => appStore.getCollapse)
watch(
  () => collapse.value,
  (newPath, oldPath) => {
    isCollapse.value = newPath
  }
)
// 监听浏览器宽度
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

/** 初始化 */
onMounted(async () => {
  getList()
  window.addEventListener('resize', handleResize)
})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.riButton {
  float: right;
  margin-bottom: 16px;
}
.rTop {
  background: #fff;
  border-radius: 2px;
  // padding: 13px 16px;
  // margin: 16px ;
  // position: sticky;
  //   top: 0;
  //   bottom: 0;
  //   z-index: 99;
  span {
    margin-right: 32px;
    cursor: default;
    font-size: 14px;
    font-weight: 400;
    display: inline-block;
    width: 120px;
    text-align: center;
    // padding: 13px 16px;
    margin: 10px 0 0 10px;
    padding: 10px 0;
  }
  // span:hover {
  //   background: #eaeced;
  //   // padding: 13px 16px;
  // }
}
.dx {
  border-bottom: 1px solid #303133;
  padding: 8px 0;
  font-weight: 500;
}
.center {
  margin-top: 55px;
}
.taUser {
  display: flex;
  align-items: center;
  .taUserLeft {
    width: 28px;
    height: 28px;
    background: #3370ff;
    border-radius: 5px;
    line-height: 28px;
    font-weight: 500;
    font-size: 12px;
    color: #ffffff;
    margin-right: 5px;
    text-align: center;
  }
  .taUser1 {
    > div:nth-of-type(1) {
      font-weight: 500;
      font-size: 14px;
      color: #303133;
      text-align: left;
    }
    > div:nth-of-type(2) {
      font-weight: 400;
      font-size: 14px;
      color: #ffa826;
      cursor: pointer;
    }
  }
}
.rowBox {
  position: fixed;
  left: 200px;
  right: 0px;
  top: 81px;
}
.isCol {
  left: 64px;
  margin-left: 0 !important;
}
.isCBox-else {
  left: 0;
}
:deep.deptClass {
  position: relative;
  // padding: 0 10px;
  .el-input {
    pointer-events: none;
  }
  .tagItem {
    position: absolute;
    left: 10px;
    top: 0;
    .el-tag {
      margin-right: 5px;
    }
    .el-tag__content {
      padding: 0 6px;
    }
  }
}
.JuF {
  margin-right: 10px;
}
</style>
