<template>
  <div class="app-main">
    <div>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="工作日名称" prop="name">
          <el-input class="!w-220px" v-model="queryParams.name" placeholder="工作日名称"></el-input>
        </el-form-item>

        <el-form-item label="年份" prop="yearName">
          <el-date-picker v-model="queryParams.yearName" type="year" format="YYYY" value-format="YYYY" class="!w-220px"
            placeholder="选择年份" />
        </el-form-item>

        <el-form-item label="创建人" prop="creator">
          <el-input class="!w-220px" v-model="queryParams.creator" placeholder="创建人"></el-input>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker class="!w-220px" format="YYYY-MM-DD" value-format="YYYY-MM-DD" v-model="createTimeRange"
            type="daterange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间"
            @change="hanlderTimeChange" />
        </el-form-item>




        <el-form-item label="工作日名称" prop="name">
          <el-date-picker v-model="selectedDates" type="dates" format="DD" value-format="DD" placeholder="选择多个日期" />
        </el-form-item>

        <el-form-item label="">
          <el-button type="primary" @click="getList">查询</el-button>
          <el-button @click="reset">重置</el-button>

        </el-form-item>
      </el-form>
    </div>
    <div class="option-btns">
      <el-button type="primary" @click="hanlderAddShow()">新增</el-button>
    </div>
    <div>
      <el-table :data="list">
        <el-table-column label="序号" type="index" width="80"></el-table-column>
        <el-table-column label="工作日名称" prop="name"></el-table-column>
        <el-table-column label="工作日年份" prop="yearName"></el-table-column>
        <el-table-column label="创建人" prop="creator"></el-table-column>
        <el-table-column label="创建时间" prop="createTime"></el-table-column>
        <el-table-column label="操作">
          <template v-slot="{ row }">
            <el-button type="text" @click="hanlderEditShow(row)">编辑</el-button>
            <el-button type="text" @click="handleConfig(row)">配置</el-button>
          </template>
        </el-table-column>
      </el-table>
      <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
        @pagination="getList" />

    </div>



    <el-dialog title="新增" v-model="addDialogVisible" width="500" top="30vh">
      <el-form :model="addForm" :rules="addFormRules" ref="addFormRef" label-position="left" label-width="120">
        <el-form-item label="工作日名称:" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入工作日名称" class="!w-220px"></el-input>
        </el-form-item>
        <el-form-item label="工作日年份:" prop="yearName">
          <el-date-picker v-model="addForm.yearName" type="year" format="YYYY" value-format="YYYY" class="!w-220px"
            placeholder="选择年份" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAdd()">
            确认
          </el-button>
        </div>
      </template>

    </el-dialog>

    <el-dialog :title="title" v-model="editDialogVisible" width="1000" top="30vh">
      <div style="color:red;margin-bottom: 20px">设置中选择日期为休息时间！</div>
      <el-form :inline="true" :model="editForm" ref="editFormRef" label-position="left" label-width="60">
        <el-form-item :label="`${item.monthName }月:`" v-for="(item, index) in editForm" :key="index">
          <el-date-picker v-model="item.selectedDates" type="dates" value-format="YYYY-MM-DD"
          format="D" placeholder="选择多个日期"
            :disabled-date="getDisabledDate(index)" :default-value="getDefaultMonthDate(index)" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleEdit">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <configTreeView  ref="configTree"     ></configTreeView>
  </div>
</template>
<script lang='ts' setup>
import configTreeView from './component/tree.vue'
import { getWorkDatePage, workDateAdd, getWorkDateSettingList, setWorkDateSettingBatch } from '@/api/system/commonAttendance';
import { ref, reactive, onMounted } from 'vue';
import { useRoute } from 'vue-router';

import {
  Plus,
  Minus
} from '@element-plus/icons-vue'
const message = useMessage()
const selectedDates = ref([])



/**
* 路由对象
*/
const route = useRoute();
/**
* 数据部分
*/
const list = ref([])
const total = ref(0);
const queryParams = reactive({
  pageNo: 1,
  pageSize: 15,
  name: "",
  creator: "",
  yearName: "",
  startDate: "",
  endDate: ""
})
const createTimeRange = ref([])


const getList = async () => {
  console.log('queryParams.value=', queryParams)
  const res = await getWorkDatePage(queryParams);
  list.value = res.data.list
  total.value = res.data.total;
}

//修改时间
const hanlderTimeChange = (val) => {
  if (!val || val.length == 0) {
    queryParams.startDate = "";
    queryParams.endDate = "";
  } else {
    queryParams.startDate = val[0];
    queryParams.endDate = val[1];
  }

  getList();
}
const queryFormRef = ref()
//重置时间
const reset = () => {
  queryFormRef.value.resetFields()
  createTimeRange.value = [];
  queryParams.startDate = "";
  queryParams.endDate = "";
  getList();
}

onMounted(() => {
  getList()
})



const addDialogVisible = ref(false)
const addForm = ref({
  name: "",
  yearName: "",
})

const addFormRef = ref()
const addFormRules = {
  name: [
    { required: true, message: '请输入工作日名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  yearName: [
    { required: true, message: '请选择工作日年份', trigger: 'blur' }
  ]
}




const handleAdd = async () => {
  addFormRef.value.validate(async (valid) => {
    if (valid) {
      const res = await workDateAdd(addForm.value);
      if (res.code == 0) {
        message.success('新增成功')
        addDialogVisible.value = false
        getList()
      } else {
        message.error('新增失败')
      }
    } else {
      console.log('error submit!!')
      return false
    }
  })
}


const hanlderAddShow = () => {
  addForm.value = {
    name: "",
    yearName: "",
  }

  addDialogVisible.value = true
}

const title = ref("")
const editDialogVisible = ref(false)

const editForm = ref([])


const hanlderEditShow = (row) => {
  yearName.value = row.yearName
  title.value = row.name
  getWorkDateSettingList({
    workDateId: row.id,
    pageNo: 1,
    pageSize: 15,
  }).then(res => {
    console.log('res.data.list', res.data)

    res.data=  res.data.map(month => ({
    ...month,
    // 将 "3,5,7,22" 转换为 ["2025-12-03", "2025-12-05"...]
    selectedDates: month.dateName ? month.dateName.split(',')
      .filter(d => d.trim()) // 过滤空值
      .map(d => {
        const day = d.padStart(2, '0');
        return `${yearName.value}-${String(month.monthName).padStart(2, '0')}-${day}`;
      }): []
  }))

    // for (let i = 0; i < res.data.length; i++) {
    //   if (res.data[i].dateName) {
    //     res.data[i].dateName = res.data[i].dateName.split(",")
    //   } else {
    //     res.data[i].dateName = []
    //   }
    // }
    console.log('res.data', res.data)
    editForm.value = res.data
    editDialogVisible.value = true
  })
}

const yearName = ref()

// 获取禁用日期函数
const getDisabledDate = (monthIndex) => (time) => {
  const start = new Date(yearName.value, monthIndex, 1);
  const end = new Date(yearName.value, monthIndex + 1, 0);
  return time < start || time > end;
}

// 生成对应年份月份的默认日期（每月1号）
const getDefaultMonthDate = (monthIndex) => {
  return new Date(yearName.value, monthIndex, 1);
};


const handleEdit = async () => {

 
  console.log('editForm.value', editForm.value)
  // editForm.value=editForm.value.map(month => ({
  //   ...month,
  //   // 转换回 "3,5,7,22" 格式
  //   dateName: month.selectedDates
  //     .map(dateStr => new Date(dateStr).getDate())
  //     .sort((a, b) => a - b)
  //     .join(',')
  // }));



  editForm.value = editForm.value.map(month => {
  // 如果 month 是 null 或 undefined，返回一个默认对象
  if (!month) {
    return { dateName: '' }; // 或者返回一个空对象，根据需求调整
  }

  return {
    ...month,
    // 转换回 "3,5,7,22" 格式
    dateName: (month.selectedDates?.map(dateStr => {
      const date = new Date(dateStr);
      return date.getDate();
    }) || [])
      .sort((a, b) => a - b)
      .join(',')
  };
});







  setWorkDateSettingBatch(editForm.value).then(res => {
    if (res.code == 0) {
      message.success('修改成功')
      editDialogVisible.value = false
      getList()
    } else {
      message.error('修改失败')
    }
  })

}


const configTree=ref()

const handleConfig = async (row) => {
  console.log("handleConfig", row)
  
  configTree.value.show(1,row.id)

}

</script>

<style scoped lang='less'>
.app-main {
  padding: 20px;
  border-radius: 10px;
  background-color: #fff;
  min-height: calc(100vh - 150px);
}

.option-btns {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: center;
}

.input-box {
  width: 300px;
}

.timeBox {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.time-input {
  width: 250px;
  margin-right: 10px;
}
</style>