<template>
  <el-row class="rowBox relative" :class="{ isCol: isCollapse, 'isCBox-else': windowWidth < 768 }">
    <!-- 左侧部门树 -->
    <el-col  :xl="3" :lg="5" :md="6" :sm="7" :xs="8" class="left-col">
      <div class="banyixia">办一下</div>
      <div class="xinJian" @click="addNew">
        <el-icon style="margin-right: 5px"><Plus /></el-icon>新建
      </div>
      <!-- <div class="left">
        <el-button type="primary" @click="addNew" style="width: 100%; height: 34px">
          <el-icon><Plus /></el-icon>
          新建</el-button
        >
      </div> -->
      <!-- <div
        v-show="!flag"
        v-for="(item, i) in leftListData"
        :key="i"
        class="leftBottom"
        :class="tableIndex == i ? 'leftBottomBg' : ''"
        @click="queryUser(item.id, i)"
      >
        <img src="../../../assets/imgs/rectangle.png" alt="" />
        <div class="tetx" :title="item.name">{{ item.name }}</div>
      </div> -->
      <div
        class="leftBottom"
        v-for="(item, i) in leftListData"
        :key="i"
        @click="queryDing(item.name, item.id)"
        :class="tableIndex == item.name ? 'leftBottomBg' : ''"
      >
        <!-- <img src="../../../assets/imgs/sd.png" alt="" /> -->
        <img :src="item.img" alt="" />
        <div style="margin-left: 4px; flex: 0.9">{{ item.name }} </div>
        <el-badge :value="badgeVal" v-if="item.name == '未读' && badgeVal != 0" class="item">
        </el-badge>
        <el-badge :value="badgeVal1" v-if="item.name == '新评论' && badgeVal1 != 0" class="item">
        </el-badge>
      </div>
    </el-col>
    <el-col  :xl="21" :lg="19" :md="18" :sm="17" :xs="16" class="gundong" v-loading="loading">
      <div class="rTop">
        {{ tableIndex }}
        <span
          v-if="tableIndex == '未读' || tableIndex == '新评论'"
          :class="userMessage.length <= 0 ? 'un-read1' : 'un-read'"
          @click="oneClickMarkRead"
        >
          <img v-if="userMessage.length <= 0" src="@/assets/imgs/onekey1.svg" alt="" />
          <img v-else src="@/assets/imgs/onekey.svg" alt="" />
          一键标为已读</span
        >
      </div>
      <div class="rBttom"  v-for="(item, i) in userMessage" :key="i" @click="viewXiangqing(item)">
        <!-- 展示公告 -->
        <div class="sendTop" v-if="item.contentUrl && item.contentUrl.includes('system/notice/')">
          <img src="../../../assets/imgs/gonggao.svg" alt="" />
          <span style="margin-left: 10px"> 公告 </span>
        </div>
        <!-- 展示头像 -->
        <div v-else-if="item.userType != 0">
          <div class="rBttomF rBttomF1" v-for="(it, index) in item.userList" :key="index">
            <div class="rightBottomFF">
              <div class="userYuan" v-if="it.userType == 0">
                {{ it.userName.length >= 3 ? it.userName.substring(1, 3) : it.userName }}
              </div>
            </div>
            <div class="username">
              {{ it.userType == 0 ? it.userName + '  ' : '' }}
            </div>
          </div>
        </div>

        <!-- 展示发给 -->
        <div
          class="sendTop"
          v-else-if="
            item.userType == 0 &&
            (!item.contentUrl || item.contentUrl.includes('/wflow/process/progress'))
          "
        >
          <div>发给:</div>
          <div class="people" v-for="(it, index) in item.userList" :key="index"
            >{{ it.userType == 1 ? it.userName : '' }}
            <span class="icPo">
              <img
                v-if="it.userType == 1 && it.readFlag == 0"
                src="../../../assets/imgs/yiduic.svg"
                alt=""
              />
              <img
                v-if="it.userType == 1 && it.readFlag == 1"
                src="../../../assets/imgs/weiduic.svg"
                alt=""
              />
            </span>

            <span v-if="index !== item.userList.length - 1 && it.userType == 1">,</span>
          </div>
          <div class="numPe">{{ item.userList.filter((item) => item.userType != 0).length }}人</div>
        </div>

        <div class="yuandain" v-show="item.readFlag != 0"></div>

        <!--  -->
        <div class="biaoti"> {{ item.content }} </div>
        <div
          class="rBttomF xiantiao"
          v-if="item.contentUrl && !item.contentUrl.includes('/system/notice/me/get')"
          @click.stop="daiBan(item.contentUrl)"
        >
          <img src="../../../assets/imgs/xiaoren.svg" alt="" />
          <!-- <div style="margin-left: 10px" v-if="item.contentUrl">
            {{ item.contentUrl.includes('/system/notice/me/get') ? item.content : '待办任务通知' }}
          </div> -->
          <div style="margin-left: 10px"> 待办任务通知 </div>
        </div>
        <div
          class="rBttomF xiantiao"
          @click.stop="daiBan(item.contentUrl)"
          v-if="item.contentUrl && item.contentUrl.includes('/system/notice/me/get')"
        >
          <img src="../../../assets/imgs/gogBi.png" alt="" />
          <div style="margin-left: 10px">
            {{ item.content }}
          </div>
        </div>
        <!--  -->
        <div class="rBttomTime">
          <div style="color: #606266; font-size: 12px">{{ item.createTime }} {{ item.hous }}</div>
          <div>
            <span style="margin-right: 37px" class="pinglun">
              <img src="../../../assets/imgs/xiaoxi.svg" alt="" />
              <span>{{ item.messageSize }}</span>
            </span>
            <span @click.stop>
              <el-dropdown @command="(command) => handleCommand(command, item.id)">
                <span class="el-dropdown-link">
                  <img src="../../../assets/imgs/gengduo.svg" alt="" />
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="c" v-if="tableIndex == '回收站'"
                      >恢复</el-dropdown-item
                    >
                    <div v-else>
                      <el-dropdown-item command="a">删除</el-dropdown-item>
                      <el-dropdown-item command="b" v-if="item.userType == 0"
                        >撤回</el-dropdown-item
                      >
                    </div>
                    <div> </div>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </span>
          </div>
        </div>
      </div>
      
      <div v-if="userMessage.length <= 0 && tableIndex == '未读'" class="juzhong">
        <img src="@/assets/imgs/onekeyzanwu.svg" alt="" />
        <p>暂无内容</p>
      </div>
      <div v-if="userMessage.length <= 0 && tableIndex == '回收站'" class="juzhong">
        <img src="@/assets/imgs/huishouzanwu.svg" alt="" />
        <p>回收站无内容</p>
      </div>
      <div v-if="userMessage.length <= 0 && tableIndex == '新评论'" class="juzhong">
        <img src="@/assets/imgs/pinglunzanwu.svg" alt="" />
        <p>暂无新动态</p>
      </div>
      <div v-if="userMessage.length <= 0 && tableIndex == '已发出'" class="juzhong">
        <img src="@/assets/imgs/fachuzanwu.svg" alt="" />
        <p>暂无内容</p>
      </div>
    </el-col>
  </el-row>

  <!--  -->
  <el-drawer v-model="drawer" title="办一下" size="600px">
    <el-form label-width="50px">
      <el-form-item>
        <div class="tite">
          <el-tooltip class="box-item" effect="dark" content="DING" placement="right">
            <img src="@/assets/imgs/sd.svg" alt="" />
          </el-tooltip>
          <div class="haha danyi">{{ xiangQingVal.content }} </div>
        </div>
      </el-form-item>
      <div
        class="rBttomF xiantiao"
        v-show="xiangQingVal.contentUrl"
        @click="daiBan(xiangQingVal.contentUrl)"
      >
        <img v-if="noticeFlag == 0" src="../../../assets/imgs/xiaoren.svg" alt="" />
        <img v-else src="../../../assets/imgs/gogBi.png" alt="" />
        <div style="margin-left: 10px"
          >{{ noticeFlag == 0 ? '待办任务通知' : xiangQingVal.content }}
        </div>
      </div>
      <el-form-item>
        <div class="fires">
          <div>{{ xiangQingVal.corrUserMap.initiater.userName }}</div>
          <div></div>
          <div>{{ xiangQingVal.createTime }} {{ xiangQingVal.hous }}</div>
        </div>
      </el-form-item>

      <el-form-item>
        <el-tooltip class="box-item" effect="dark" content="接收人" placement="right">
          <img src="@/assets/imgs/renyuan.svg" alt="" />
        </el-tooltip>
        <div class="haha">
          <span @click="yiDu" :style="{ color: yiduFlag == 1 ? '' : '#a2a3a5', cursor: 'pointer' }"
            >已读 <span>{{ xiangQingVal.corrUserMap.readList.length }}</span></span
          >
          <span @click="weiDu" :style="{ color: yiduFlag == 2 ? '' : '#a2a3a5', cursor: 'pointer' }"
            >未读 <span>{{ xiangQingVal.corrUserMap.unreadList.length }}</span></span
          >
          <img
            v-if="zShouSuoFlag"
            style="margin-left: 398px"
            src="@/assets/imgs/zhankai.svg"
            alt=""
            @click="zShouSuoFlag = !zShouSuoFlag"
          />
          <img
            v-if="!zShouSuoFlag"
            style="margin-left: 398px"
            src="@/assets/imgs/zhankai1.svg"
            alt=""
            @click="zShouSuoFlag = !zShouSuoFlag"
          />
        </div>
      </el-form-item>
      <!--  -->
      <el-form-item v-if="zShouSuoFlag">
        <div v-if="yiduFlag == 1" class="allReadNot">
          <div class="bottomUser" v-for="(item, i) in xiangQingVal.corrUserMap.readList" :key="i">
            <div>{{
              item.userName.length >= 3 ? item.userName.substring(1, 3) : item.userName
            }}</div>
            <!-- <div>{{
              item.userName.length >= 3 ? item.userName.substring(1, 4) : item.userName
            }}</div> -->
            <div>{{ item.userName }}</div>
          </div>
        </div>
        <div v-else-if="yiduFlag == 2" class="allReadNot">
          <div class="bottomUser" v-for="(item, i) in xiangQingVal.corrUserMap.unreadList" :key="i">
            <div>{{
              item
                ? item.userName.length >= 3
                  ? item.userName.substring(1, 3)
                  : item.userName
                : ''
            }}</div>
            <!-- <div>{{
              item
                ? item.userName.length >= 3
                  ? item.userName.substring(1, 4)
                  : item.userName
                : ''
            }}</div> -->
            <div>{{ item ? item.userName : '' }}</div>
          </div>
        </div>
      </el-form-item>
      <!--  -->
      <el-form-item v-if="xiangQingVal.messageList.length > 0">
        <el-tooltip class="box-item" effect="dark" content="评论" placement="right">
          <img src="@/assets/imgs/xiaoxi.svg" alt="" />
        </el-tooltip>
        <div class="haha">
          <span
            >全部 <span>{{ xiangQingVal.messageList.length }}</span></span
          >
          <!-- <span @click="weiDu" :style="{ color: yiduFlag ? '#a2a3a5' : '', cursor: 'pointer' }"
            >只看回复 <span>{{ xiangQingVal.corrUserMap.unreadList.length }}</span></span
          > -->
        </div>
      </el-form-item>
      <div class="centPinlun" v-for="(item, i) in xiangQingVal.messageList" :key="i">
        <div class="pinlun">
          <div>{{
            item.creatorName.length >= 3 ? item.creatorName.substring(1, 3) : item.creatorName
          }}</div>
          <div class="pinXiaox" @mouseover="showDeleButton(i)" @mouseleave="hideDeleButton">
            <div class="tanQ">
              <div>{{ item.title }}</div>
              <div
                class="huifu"
                @click="reply(item)"
                v-if="xiangQingVal.respSendFlag == 0 && item.userType == 1"
                >回复</div
              >
            </div>
            <!--  -->
            <div class="tanQ">
              <div
                class="delde"
                @click="deleMess(item.id)"
                v-if="isVisible && item.userType == 0 && tabIndex == i"
                >删除</div
              >

              <div>{{ item.createTime }} {{ item.hous }}</div>
            </div>
          </div>
        </div>
        <div class="centPB">{{ item.content }}</div>
      </div>
    </el-form>
    <template #footer>
      <el-input
        v-model="inputVal"
        :placeholder="placeholderText"
        @keyup.enter="handleEnter(xiangQingVal)"
      ></el-input>
    </template>
  </el-drawer>
  <!--  -->
  <createNewOne ref="createOne" @success="dingPage" />
  <el-drawer
    :size="isMobile ? '100%' : '560px'"
    direction="rtl"
    title="审批详情"
    v-model="processVisible"
    class="custom-detail-header"
  >
    <instance-preview
      v-if="processVisible"
      :newUrl="propObj.newUrl"
      :instanceId="propObj.instanceId"
      :nodeId="propObj.nodeId"
      @handler-after="handlerAfter"
    ></instance-preview>
  </el-drawer>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as RoleApi from '@/api/system/role'
import { getUserProfile } from '@/api/system/user/profile'
import { Plus, CaretBottom, ArrowRight } from '@element-plus/icons-vue'
import { tr } from 'element-plus/es/locale'
import { useAppStore } from '@/store/modules/app'
import * as orderItApi from '@/api/system/orderIt'
import { dateFormatter } from '@/utils/formatTime'
import InstancePreview from '@/views/wflow/workspace/approval/ProcessInstancePreview1.vue'

import dayjs from 'dayjs'
import type { DropdownInstance, ElMessageBox } from 'element-plus'
const dropdown1 = ref<DropdownInstance>()
defineOptions({ name: 'orgstructure' })
import createNewOne from './createNewOne.vue'
import { factory } from 'typescript'
import { useRouter } from 'vue-router'
const router = useRouter()
const drawer = ref(false)
const noticeFlag = ref(0)
const xiangQingVal = ref({})
const isMobile = computed(() => window.screen.width < 450)
const processVisible = ref(false)
const propObj = ref({
  newUrl: null,
  instanceId: null,
  nodeId: null
})
const inputVal = ref('')
const isReply = ref(false)
const replyCreator = ref('')
const placeholderText = ref('请输入评论内容')
const badgeVal = ref(null)
const badgeVal1 = ref(null)
const yiduFlag = ref(1)
const zShouSuoFlag = ref(true)
const userMessage = ref([]) // 消息弹窗
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const createOne = ref()
const tableIndex = ref('全部')
const leftListData = ref([
  {
    name: '全部',
    id: '',
    img: new URL('@/assets/imgs/sd.svg', import.meta.url)
  },
  {
    name: '未读',
    id: '0',
    img: new URL('@/assets/imgs/weidu.svg', import.meta.url)
  },
  {
    name: '已发出',
    id: '1',
    img: new URL('@/assets/imgs/yifachu.svg', import.meta.url)
  },
  {
    name: '新评论',
    id: '2',
    img: new URL('@/assets/imgs/xinpinglun.svg', import.meta.url)
  },
  {
    name: '回收站',
    id: '3',
    img: new URL('@/assets/imgs/huishouzhan.svg', import.meta.url)
  }
])
const userInfo = ref({})
const loading = ref(false)

const handleCommand = async (command, id) => {
  // console.log(command)
  if (command == 'a') {
    await message.delConfirm()
    await orderItApi.deleteDing(id)
    message.success(t('common.delSuccess'))
    await dingPage()
  } else if (command == 'b') {
    ElMessageBox.confirm('是否撤回所选中数据？', '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        await orderItApi.cancelDing(id)
        message.success('撤销成功')
        await dingPage()
      })
      .catch(() => {})
  } else {
    ElMessageBox.confirm('是否恢复所选中数据？', '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        await orderItApi.recoveryDing(id)
        message.success('恢复成功')
        await dingPage(queryDingId.value)
      })
      .catch(() => {})
  }
}

// 获取用户个人登录信息
// const getUserInfo = async () => {
//   const users = await getUserProfile()
//   userInfo.value = users
// }

// 监听折叠面板
const appStore = useAppStore()
const collapse = computed(() => appStore.getCollapse)
const isCollapse = computed(() => appStore.getCollapse)
watch(
  () => collapse.value,
  (newPath, oldPath) => {
    isCollapse.value = newPath
  }
)
// 监听浏览器宽度
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
}
const queryDingId = ref('')
// 点击左侧菜单栏
const queryDing = (name, id) => {
  tableIndex.value = name
  queryDingId.value = id
  dingPage(id)
}
// 新建
const addNew = async () => {
  createOne.value.open()
}
const badgeVal1List = ref([])
const badgeValWeidu = ref([])
const flagBadge = ref(true)
// 获取列表数据
const dingPage = async (status) => {
  loading.value = true
  const data = await orderItApi.dingPage({ status })
  console.log(data)
  userMessage.value = data.list
  loading.value = false
  data.list.map((item) => {
    item.createTime = dayjs(item.createTime).format('YYYY-MM-DD HH:mm')
    item.hous = item.createTime.substring(10)
    item.createTime = dayjs(item.createTime).format('M月D日')
  })
  // if (flagBadge.value) {
  if (status === '0') {
    badgeValWeidu.value = data.list
    badgeVal.value = data.total
  }
  if (status == 2) {
    badgeVal1List.value = data.list
    badgeVal1.value = data.total
    flagBadge.value = false
  }
  // }

  // console.log(data)
}
// 鼠标悬浮
const isVisible = ref(false)
const tabIndex = ref(null)
const viewObj = ref({})
const showDeleButton = (i) => {
  tabIndex.value = i
  isVisible.value = true
}
// 鼠标悬浮
const hideDeleButton = () => {
  isVisible.value = false
}
// 删除评论
const deleMess = async (id) => {
  await message.delConfirm()
  await orderItApi.deleteDingMessage(id)
  message.success(t('common.delSuccess'))
  await dingGet(viewObj.value)
}
// 回复评论
const reply = async (val) => {
  console.log(val)
  placeholderText.value = `回复${val.creatorName}评论:`
  isReply.value = true
  replyCreator.value = val.creator
}

// 三个点小图标
const showClick = () => {
  if (!dropdown1.value) return
  dropdown1.value.handleOpen()
}
// 评论
const handleEnter = async (val) => {
  console.log(val, '评论')
  // console.log(isReply.value)
  let obj
  // if (val.respSendFlag &&val.respSendFlag == 0) {
  //   if (val.userType == 0) {
  //     obj = {
  //       dingId: val.id,
  //       content: inputVal.value,
  //       messageType: 1
  //     }
  //   }
  // } else {
  //   if (!isReply.value) {
  //     obj = {
  //       dingId: val.id,
  //       content: inputVal.value,
  //       messageType: 0
  //     }
  //   } else {
  //     obj = {
  //       dingId: val.id,
  //       content: inputVal.value,
  //       messageType: 2,
  //       recipientIds: [replyCreator.value]
  //     }
  //   }
  // }
  console.log(obj)

  if (!inputVal.value) {
    message.warning('评论内容不能为空')
    return
  }
  if (val.respSendFlag || val.respSendFlag == 0) {
    console.log(val.respSendFlag)
    console.log('吱吱吱吱')
    if (val.respSendFlag == 0) {
      if (val.userType == 0) {
        if (!isReply.value) {
          obj = {
            dingId: val.id,
            content: inputVal.value,
            messageType: 1
          }
        } else {
          obj = {
            dingId: val.id,
            content: inputVal.value,
            messageType: 2,
            recipientIds: [replyCreator.value]
          }
        }
      } else if (val.userType == 1) {
        obj = {
          dingId: val.id,
          content: inputVal.value,
          messageType: 0
        }
      }
    } else if (val.respSendFlag == 1) {
      obj = {
        dingId: val.id,
        content: inputVal.value,
        messageType: 0
      }
    }
  }

  await orderItApi.createDingMessage(obj)
  isReply.value = false
  inputVal.value = ''
  placeholderText.value = `请输入评论内容`
  await dingGet(val)
  leftListData.value.map((item) => {
    if (item.name == tableIndex.value) {
      dingPage(item.id)
    }
  })
}
const viewXiangqing = async (val) => {
  console.log(val)
  if (val.contentUrl && val.contentUrl.includes('/system/notice/me/get')) {
    noticeFlag.value = 1
  } else {
    noticeFlag.value = 0
  }
  isReply.value = false
  inputVal.value = ''
  placeholderText.value = `请输入评论内容`
  viewObj.value = val
  await dingGet(val)
  zShouSuoFlag.value = true
  drawer.value = true
  //  如果是已读的就不调接口 readFlag: 0代表已读 不为0代表未读
  // if (val.readFlag != 0) { 旧的改动
    if (tableIndex.value == '未读') {
      await dingPage('0')
    } else if (tableIndex.value == '新评论') {
      await dingPage('2')
    } else if (tableIndex.value == '全部') {
      await dingPage('0')
      await dingPage('2')
      await dingPage()
    }
  // }

  console.log(tableIndex.value, 'sdaaaaaaaaaaaaaaaaaaaaaaaaaa')

  // await dingPage()
  // if (tableIndex.value == '新评论') {
  //   // badgeVal1List
  //   badgeVal1List.value.map((item, i) => {
  //     if (item.id == val.id) {
  //       badgeVal1List.value.splice(i, 1)
  //     }
  //   })
  //   badgeVal1.value = badgeVal1List.value.length
  // }
  // if (tableIndex.value == '未读') {
  //   // badgeVal1List
  //   badgeValWeidu.value.map((item, i) => {
  //     if (item.id == val.id) {
  //       badgeValWeidu.value.splice(i, 1)
  //     }
  //   })
  //   badgeVal.value = badgeValWeidu.value.length
  // }
}
const dingGet = async (val) => {
  const data = await orderItApi.dingGet({ id: val.id })
  // console.log(data, 'dasdasadas')
  data.createTime = dayjs(data.createTime).format('YYYY-MM-DD HH:mm')
  data.hous = data.createTime.substring(10)
  data.createTime = dayjs(data.createTime).format('M月D日')
  data.messageList.map((item) => {
    item.createTime = dayjs(item.createTime).format('YYYY-MM-DD HH:mm')
    item.hous = item.createTime.substring(10)
    item.createTime = dayjs(item.createTime).format('M月D日')
  })
  xiangQingVal.value = data
}
const daiBan = (val) => {
  console.log(val, 'dsads')
  if (val.includes('/system/notice/me/get')) {
    router.push({ path: '/notify/list/person', query: { newURL: val, URlFlag: 1 } })
    // router.push('/notify/list/person')
    // router.push({path:'/notify/list/person', state: {newURL: JSON.stringify(val)}},)
  } else {
    let newVal = val.split('/')
    // 获取数组的最后一个元素
    var lastElement = newVal[newVal.length - 1]
    // 获取数组的倒数第二个元素
    var secondLastElement = newVal[newVal.length - 2]
    // console.log('最后一个元素：', lastElement)
    // console.log('最后二个元素：', secondLastElement)
    propObj.value.newUrl = val
    propObj.value.instanceId = secondLastElement
    propObj.value.nodeId = lastElement
    processVisible.value = true
  }
}
const handlerAfter = () => {
  processVisible.value = false
}
// 已读
const yiDu = () => {
  yiduFlag.value = 1
  zShouSuoFlag.value = true
}
// 未读
const weiDu = () => {
  yiduFlag.value = 2
  zShouSuoFlag.value = true
}
// 一键标为已读
const oneClickMarkRead = async () => {
  if (userMessage.value.length <= 0) return
  await message.confirm('确认将这些BAN消息全部设为已读吗？')
  if (tableIndex.value == '未读') {
    await orderItApi.dingAllSetRead()
  } else if (tableIndex.value == '新评论') {
    await orderItApi.dingMessageAllSetRead()
  }
  message.success('操作成功')
  // await dingPage('0')
  leftListData.value.map((item) => {
    if (item.name == tableIndex.value) {
      dingPage(item.id)
    }
  })
}
/** 初始化 */
onMounted(async () => {
  await dingPage('0')
  await dingPage('2')
  await dingPage()
  window.addEventListener('resize', handleResize)
})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.left {
  display: flex;
  align-items: center;
  padding: 12px 8px;
  border-radius: 8px;
  margin-bottom: 5px;
  cursor: pointer;
  margin: 8px 8px;

  img {
    width: 30px;
    height: 30px;
    border-radius: 5px;
  }
  .el-icon {
    color: #a2a3a4;
  }
  .tetx {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 10px;
    margin-right: 4px;
    font-size: 14px;
    color: #301333;
  }
}

// .leftBottom {
//   display: flex;
//   align-items: center;
//   padding: 12px 8px 12px 19px;
//   border-radius: 8px;
//   margin-bottom: 5px;
//   cursor: pointer;
//   margin: 8px 8px;

//   .tetx {
//     white-space: nowrap;
//     overflow: hidden;
//     text-overflow: ellipsis;
//     margin-left: 20px;
//     font-size: 14px;
//   }
// }

.leftBottom {
  display: flex;
  align-items: center;
  width: 175px;
  height: 36px;
  margin: 0 auto;
  border-radius: 8px;
  padding: 0px 0px 0px 8px;
  font-size: 14px;
  margin-bottom: 10px;
}

.leftBottom:hover {
  background-color: #f3f4f7;
}

.leftBottomBg {
  background-color: #f3f4f7;
}
.rTop {
  background: #fff;
  border-radius: 2px;
  padding: 13px 16px;
  position: sticky;
  top: 0;
  bottom: 0;
  z-index: 99;
}
.gundong {
  overflow-y: auto;
}
/* 自定义整个滚动条 */
.gundong::-webkit-scrollbar {
  width: 12px; /* 设置滚动条的宽度 */
  background-color: #f9f9f9; /* 滚动条的背景色 */
}

/* 自定义滚动条轨道 */
.gundong::-webkit-scrollbar-track {
  background: #e1e1e1; /* 轨道的背景色 */
  border-radius: 10px; /* 轨道的圆角 */
}

/* 自定义滚动条的滑块（thumb） */
.gundong::-webkit-scrollbar-thumb {
  background-color: #c1c1c1; /* 滑块的背景色 */
  border-radius: 10px; /* 滑块的圆角 */
  border: 2px solid #ffffff; /* 滑块边框 */
}

/* 当滑块悬停或活动时的样式 */
.gundong::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8; /* 悬停或活动状态下滑块的背景色 */
}
.rBttom {
  // width: 1556px;
  background: #fff;
  // margin-top: 12px;
  margin: 12px;
  border-radius: 14px;
  font-size: 14px;
  cursor: pointer;
  padding-top: 16px;
  position: relative;
}
// .yiti {
//   margin-bottom: 12px;
// }
.rBttomF {
  display: flex;
  align-items: center;
  // padding: 16px 0 0 16px;
}
.xiantiao {
  width: 274px;
  border-radius: 4px;
  border: 1px solid #eceded;
  margin-left: 16px;
  margin-top: 13px;
  padding: 8px;
}
.rBttomF1 {
  // padding: 16px 0 0 16px;
  margin: 0 0 0 16px;
}

.yuandain {
  width: 8px;
  height: 8px;
  background: #ff5757;
  border-radius: 40px;
  position: absolute;
  right: 23px;
  top: 23px;
}
.biaoti {
  padding: 18px 0 0 16px;
  word-wrap: break-word;
}
.rBttomTime {
  display: flex;
  justify-content: space-between;
  padding: 16px;
}

.right {
  .rightTop {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 16px;
    padding: 16px 0;
    border-bottom: 1px solid #e9eced;
    color: #303133;

    .rightTopL {
      display: flex;
      align-items: center;

      .rightTopImg {
        width: 30px;
        height: 30px;
        border-radius: 5px;
      }

      .text {
        margin-left: 15px;

        // margin-bottom: 15px; //先注释后面需要放开
        .textB {
          color: #9da0a1;
          font-size: 14px;
          // margin-top: 8px;

          > span {
            border: 1px solid #ebedee;
            border-radius: 5px;
            padding: 5px;
            margin-right: 15px;
          }
        }
      }
    }

    .rightTopR {
      img {
        margin-right: 20px;
      }
    }
  }

  .xian {
    width: 100%;
    height: 1px;
    background: #eaeced;
    margin-bottom: 15px;
  }
}

.rightC {
  margin: 16px 0;
  overflow-y: auto;
  .rightCT {
    margin-bottom: 20px;
    padding: 0 16px;
  }

  .rightBottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 5px;
    padding: 8px 6px;
    margin: 8px 10px;
    cursor: pointer;
    font-size: 14px;

    img {
      vertical-align: middle;
      width: 36px;
      height: 36px;
    }
  }
}

.rightBottom:hover {
  background-color: #f3f4f7;
}

:deep.disabled {
  cursor: not-allowed !important;
}

:deep.addressBook {
  .el-card {
    border: none;
  }

  .el-card__body {
    padding: 0 !important;
  }

  .container {
    position: relative;
    width: 240px;

    .big-image {
      width: 100%;
      height: 100%;
    }

    .small-image {
      position: absolute;
      top: 12px;
      right: 12px;
    }
  }

  .detailsleft {
    width: 240px;
    height: 320px;
    background: #ffffff;
    border-radius: 10px;
    position: relative;
    top: -10px;

    .nameT {
      width: 118px;
      height: 118px;
      border-radius: 10px;
      background: #007fff;
      color: #ffffff;
      text-align: center;
      line-height: 118px;
      font-size: 40px;
      font-weight: 500;
      position: absolute;
      top: -50px;
      left: 18px;
      border: 2px solid #ffffff;
    }

    .aniu {
      position: absolute;
      right: 12px;
      top: 12px;
    }

    .aniu1 {
      width: 240px;
      position: absolute;
      bottom: 12px;
      text-align: center;
    }

    .frie {
      background: #eaf0ff;
      color: #007fff;
      border-color: #eaf0ff;

      img {
        margin-right: 3px;
      }
    }

    .cter {
      position: absolute;
      top: 84px;
      left: 16px;

      .nam {
        font-weight: 500;
        font-size: 20px;
        color: #171a1d;
      }

      .ente {
        font-weight: 400;
        font-size: 14px;
        color: #737677;
        margin-top: 8px;
      }

      img {
        margin-right: 18px;
        margin-top: 14px;
      }
    }
  }

  .detailsR {
    width: 276px;
    height: 216px;
    background: #ffffff;
    border-radius: 8px;
    margin-top: 9px;
  }

  .detailsRText {
    padding: 16px 0 0 12px;
    display: flex;

    .detailsRLeft {
      flex: 0.4;
      font-weight: 400;
      font-size: 14px;
      color: #737677;
    }

    .detailsRLeft1 {
      flex: 1;
      font-weight: 400;
      font-size: 14px;
      color: #171a1d;
    }

    .detailsRLeft2 {
      font-weight: 400;
      font-size: 14px;
      color: #171a1d;
      flex: 0.56;
      margin-right: 40px;
    }

    .textColor {
      color: #007fff;
    }
  }

  .detailsRB {
    width: 276px;
    height: 174px;
    background: #ffffff;
    border-radius: 8px;
    margin-top: 10px;
  }
}

.rowBox {
  margin: 0 0 0 20px;
  position: fixed;
  left: 180px;
  right: 15px;
  top: 81px;
  bottom: 0;
}
.el-col {
  height: 100%;
}
.left-col {
  padding: 0 !important;
  border-right: 1px solid #eceded;
  box-sizing: border-box;
  background: #fff;
}
.el-breadcrumb__inner a,
.el-breadcrumb__inner.is-link {
  color: #3370ff;
  font-weight: normal;
}
.el-breadcrumb__separator {
  color: #a2a3a4;
}
.el-breadcrumb__item:last-child .el-breadcrumb__inner,
.el-breadcrumb__item:last-child .el-breadcrumb__inner a,
.el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover,
.el-breadcrumb__item:last-child .el-breadcrumb__inner:hover {
  color: #a2a3a4;
}
.isCol {
  left: 64px;
  margin-left: 0 !important;
}
.isCBox-else {
  left: 0;
}
.el-col {
  height: 100%;
  // overflow: auto;
}
// 新
.rightBottomFF {
  display: flex;
  align-items: center;
  .userYuan {
    width: 36px;
    height: 36px;
    background: #3370ff;
    border-radius: 5px;
    text-align: center;
    line-height: 36px;
    color: #ffffff;
    box-sizing: border-box;
    font-size: 12px;
  }
}
.username {
  margin-left: 6px;
}
.pinglun {
  img {
    vertical-align: middle;
  }
  span {
    font-size: 12px;
    color: #c7c7c8;
    margin-left: 3px;
  }
}
.bottomUser {
  display: flex;
  align-items: center;
  background: #f3f4f7;
  border: 1px solid #f3f4f7;
  border-radius: 4px;
  padding: 5px;
  margin-right: 10px;
  margin-top: 10px;

  > div:nth-of-type(1) {
    height: 24px;
    line-height: 24px;
    background: #3370ff;
    border-radius: 4px;
    color: #fff;
    padding: 3px;
    font-size: 10px;
  }
  > div:nth-of-type(2) {
    margin: 0 10px;
    font-size: 12px;
  }
}
.fires {
  display: flex;
  align-items: center;
  margin-left: 30px;

  > div:nth-of-type(1) {
    color: #307ed0;
  }
  > div:nth-of-type(2) {
    width: 1px;
    height: 18px;
    background: #eceded;
    margin: 0 10px;
  }
  > div:nth-of-type(3) {
    color: #a2a3a5;
  }
}
:deep .el-form-item__content {
  margin-left: 0 !important;
}
.haha {
  margin-left: 10px;
  > span:nth-of-type(2) {
    margin-left: 10px;
  }
}
.centPinlun {
  margin-left: 31px;
  margin-bottom: 20px;
  .pinlun {
    display: flex;
    align-items: center;
    font-size: 14px;
    > div:nth-of-type(1) {
      height: 24px;
      line-height: 24px;
      background: #3370ff;
      border-radius: 4px;
      color: #fff;
      padding: 0 3px;
      font-size: 10px;
    }
    .pinXiaox {
      width: 90%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;
      margin-left: 10px;
      color: #a2a3a5;
      > div:nth-of-type(1) {
        // margin-right: 155px;
      }
      .delde {
        margin-right: 15px;
        color: #307ed0;
        cursor: pointer;
        // margin-left: 150px;
      }
      .huifu {
        padding: 3px;
        border: 1px solid #2190ff;
        border-radius: 2px;
        // margin-right: 98px;
        margin-left: 19px;
        color: #2190ff;
        cursor: pointer;
      }
      .tanQ {
        display: flex;
        align-items: center;
      }
    }
  }
  .centPB {
    margin-left: 38px;
  }
}
.banyixia {
  // margin: 13px 0 0 12px;
  width: 175px;
  margin: 0 auto;
  padding: 8px 0 0 0;
}
.xinJian {
  width: 183px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #3370ff;
  border-radius: 8px;
  margin: 0 auto;
  color: #ffffff;
  line-height: 36px;
  margin-top: 20px;
  margin-bottom: 15px;
  cursor: pointer;
}
.allReadNot {
  display: flex;
  margin-left: 30px;
  flex-wrap: wrap;
}
.sendTop {
  display: flex;
  align-items: center;
  margin-left: 16px;
  > div:nth-of-type(1) {
    color: #a2a3a5;
    margin-right: 10px;
  }
  .people {
    color: #3370ff;
    margin-right: 3px;
    .icPo {
      position: relative;
      top: -7px;
    }
  }
  .numPe {
    margin-left: 15px;
  }
}
.tite {
  display: flex;
  align-items: flex-start;
  width: 100%;
  line-height: 22px;
  .danyi {
    width: 95%;
    word-wrap: break-word;
  }
}
.el-dropdown-link:focus-visible {
  outline: unset;
}
.un-read {
  float: right;
  color: #303133;
  cursor: pointer;
  img {
    vertical-align: middle;
  }
}
.un-read1 {
  float: right;
  cursor: not-allowed;
  color: #c0c4cc;
  img {
    vertical-align: middle;
  }
}
.juzhong {
  position: absolute;
  left: 50%;
  top: 38%;
  //transform: translate(-50%, -50%);
  p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #909399;
    text-align: center;
    font-style: normal;
  }
}
</style>
