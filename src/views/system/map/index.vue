<template>
  <div class="box">
    <div id="container" style="width: 100%; height: 600px; position: relative">
      <!-- <input
        class="keyword"
        id="keyword"
        placeholder="请输入搜索位置"
        style="position: absolute; z-index: 99"
      /> -->
      <el-input v-model="keyword" class="keyword" id="keyword" placeholder="" style="position: absolute; z-index: 99"></el-input>
    </div>
  </div>

  <!-- <ContentWrap class="h-1/1"> </ContentWrap> -->
</template>

<script lang="ts" setup>
import AMapLoader from '@amap/amap-jsapi-loader'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as RoleApi from '@/api/system/role'
import { getUserProfile } from '@/api/system/user/profile'
import { CaretTop, CaretBottom, ArrowRight } from '@element-plus/icons-vue'
import { tr } from 'element-plus/es/locale'
defineOptions({ name: 'orgstructure' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const queryParams = reactive({
  id: ''
})
const keyword = ref('')
// 存储搜索用的数据
const form: any = reactive({
  address: ''
})
let map = null
const ininMap = () => {
  window._AMapSecurityConfig = {
    securityJsCode: '6b7155cc707ec47aed474972a96b1add' //密钥
  }
  AMapLoader.load({
    key: '63a96bbd4c3209cf856c96d61345a2b7', //api服务key--另外需要在public中使用安全密钥！！！
    version: '1.4.4' // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    // plugins: ['AMap.PlaceSearch', 'AMap.AutoComplete'] // 需要使用的的插件列表
  })
    .then((AMap) => {
      map = new AMap.Map('container', {
        resizeEnable: true,
        zoom: 9.5, // 地图显示的缩放级别
        center: [117.2, 31.8]
      })
      AMap.plugin(['AMap.Autocomplete', 'AMap.PlaceSearch', 'AMap.Geocoder'], function () {
        const autoOptions = {
          input: 'keyword' // 使用联想输入的input的id
        }
        const autocomplete = new AMap.Autocomplete(autoOptions)
        const placeSearch = new AMap.PlaceSearch({
          map: map
        })
        autocomplete.on('select', select) //注册监听，当选中某条记录时会触发
        function select(e) {
          console.log(e,'搜搜eeee');
          
          placeSearch.setCity(e.poi.adcode)
          placeSearch.search(e.poi.name,function(status, result){
            console.log(status);
            console.log(result);
            
          }) //关键字查询查询
        }
        // const geocoder = new AMap.Geocoder({
        //   radius: 1000,
        //   extensions: 'all'
        // })
        // AMap.event.addListener(autocomplete, 'select', function (e) {
        //   placeSearch.setCity(e.poi.adcode)
        //   placeSearch.search(e.poi.name, function (status, result) {
        //     const pois = result.poiList.pois
        //     for (let i = 0; i < pois.length; i++) {
        //       if (pois[i].name === e.poi.name) {
        //         console.log('搜索结果', pois[i])
        //         geocoder.getAddress(
        //           [pois[i].location.lng, pois[i].location.lat],
        //           function (status, result) {
        //             console.log(result)
        //             if (status === 'complete' && result.info === 'OK') {
        //               form.address = result.regeocode.formattedAddress
        //             } else {
        //               form.address = ''
        //             }
        //           }
        //         )
        //       }
        //     }
        //   })
        // })
      })
    })
    .catch((e) => {})
}

/** 初始化 */
onMounted(() => {
  ininMap()
})
</script>

<style lang="scss" scoped>
.info {
  padding: 0.5rem 0.7rem;
  margin-bottom: 1rem;
  border-radius: 0.25rem;
  position: fixed;
  top: 1rem;
  background-color: white;
  width: auto;
  min-width: 15rem;
  border-width: 0;
  right: 1rem;
  box-shadow: 0 2px 6px 0 rgba(240, 131, 0, 0.5);
  .input-item {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    height: 2.2rem;
    border: 1px solid red;
    border-radius: 0.2rem;
    .input-item-prepend {
      margin-right: -1px;
    }
    .input-item-prepend {
      width: 35%;
      font-size: 13px;
      border-right: 1px solid red;
      height: 100%;
      display: flex;
      align-items: center;
      background: rgba(240, 131, 0, 0.1);
      span {
        text-align: center;
      }
    }
    input {
      width: 60%;
      background: #fff;
      padding: 0.2rem 0.6rem;
      margin-left: 0.3rem;
      border: none;
    }
  }
}
</style>
