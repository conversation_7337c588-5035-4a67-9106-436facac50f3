<template>
  <!-- 导出记录弹窗 -->
  <el-drawer title="导出记录" v-model="dialogVisible" class="custom-detail-header myHeader" :size="drawerSize">
    <div v-for="(item, index) in tableData" :key="index" v-loading="loading" class="contentDiv">
      <div class="div1">
        <span class="span1">{{ item.fileName }}</span>
        <span class="span2">{{ item.createTime }}</span>
      </div>
      <p class="p2">日期范围：{{ item.beginDate }} - {{ item.endDate }}</p>
      <p class="p2">人员范围：{{ item.exportPersonModel ? getPeople(item.exportPersonModel) : '全公司' }}</p>
      <div class="div1 btnDiv">
        <div>
          <el-button type="text" icon="el-icon-check" class="successBtn" v-if="item.downloadUrl">导出成功</el-button>
          <el-button type="text" :loading="true" class="loadingBtn" v-else>导出中</el-button>
        </div>
        <el-button type="text" icon="el-icon-download" @click="handelExport(item.downloadUrl)"
          v-if="item.downloadUrl">下载</el-button>
      </div>
    </div>
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </el-drawer>
</template>
<script setup lang="ts">
import { attendanceApi } from '@/api/system/attendance/index'

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const loading = ref(false)
const dialogVisible = ref(false) // 弹窗是否展示
const tableData = ref([])
const total = ref(0)
const reportType = ref('')
const drawerSize = ref('31%')//抽屉尺寸大小
/** 打开弹窗 */
const open = async (e) => {
  drawerSize.value = '31%'
  window.addEventListener('resize', handleResize)
  handleResize()
  dialogVisible.value = true
  reportType.value = e
  getList()
}
const getList = async () => {
  loading.value = true
  try {
    const res = await attendanceApi.reportRecord({
      reportType: reportType.value,
      ...queryParams
    })
    total.value = res.total
    tableData.value = res.list
    filterList()
  } finally {
    loading.value = false
  }
}
let timerId = ref(null)
const filterList = () => {
  const hasEmptyUrl = tableData.value.some(item => !item.downloadUrl)
  if (hasEmptyUrl) {
    setTime()
  } else {
    clearTimeout(timerId.value)
  }
  console.log(hasEmptyUrl)
}
const setTime = () => {
  const timerId = setTimeout(() => {
    getList()
  }, 3000)
}
// 下载链接
const handelExport = (e) => {
  let a = document.createElement('a')
  a.href = e
  a.click()
}
// 获取人员范围
const getPeople = (e) => {
  if (e.length > 0) {
    const arr = []
    e.forEach((item) => {
      arr.push(item.name)
    })
    return arr.join(',')
  }
}
// 监听浏览器的窗口事件
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
  if (window.innerWidth > 1650) {
    drawerSize.value = '31%'
  }
  if (window.innerWidth <= 1650) {
    drawerSize.value = '45%'
  }
  if (window.innerWidth <= 1500) {
    drawerSize.value = '50%'
  }
  if (window.innerWidth < 1050) {
    drawerSize.value = '70%'
  }
  if (window.innerWidth < 700) {
    drawerSize.value = '99%'
  }
}

defineExpose({ open })
</script>
<style lang="less" scoped>
p {
  margin: 0;
  padding: 0;
}

.contentDiv {
  padding: 16px 0;
  border-bottom: 1px solid rgba(0, 0, 0, .12);
}

.el-button {
  height: 24px;
}

.successBtn {
  color: #15bc83;
  pointer-events: none;
}

.loadingBtn {
  color: #409eff;
}

.div1 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;

  .span1 {
    color: #191f25;
    flex: 1;
  }

  .span2 {
    color: rgba(25, 31, 37, .28);
    width: 140px;
    text-align: right;
    font-size: 14px;
  }
}

.p2 {
  color: rgba(25, 31, 37, .56);
  margin-bottom: 8px;
  font-size: 14px;
}

.btnDiv {
  margin-bottom: 0;
}
</style>
<style>
.myHeader .el-drawer__body {
  padding: 0px 30px !important;
}
</style>
