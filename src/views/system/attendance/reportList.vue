<template>
  <el-card class="oaCard" v-if="isShow == 1">
    <!-- <el-form :inline="true" :model="queryParams" @keydown.enter.prevent>
      <el-form-item prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入报表名称" clearable class="!w-240px" @change="handleSearch" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-upload">批量导出</el-button>
      </el-form-item>
    </el-form> -->
    <el-table v-loading="loading" :data="dataList" :show-overflow-tooltip="true">
      <el-table-column label="名称" prop="entity.name" min-width="180" />
      <el-table-column label="报表描述" prop="entity.desc" min-width="220" />
      <el-table-column label="最后修改" prop="entity.latestModifiedDate" :formatter="dateFormatter" min-width="190" />
      <el-table-column label="操作" min-width="200">
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)"
            :disabled="scope.row.entity.name != '原始记录'">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </el-card>
  <!-- 详情 -->
  <detail ref="detailRef" v-if="isShow == 2" @close="handleClose" />
</template>
<script setup type="ts">
import { dateFormatter } from '@/utils/formatTime'
import { attendanceApi } from '@/api/system/attendance/index'
import detail from './reportDetail.vue'
defineOptions({ name: 'attendanceReportList' })

const queryParams = reactive({
  name: '',
  pageNo: 1,
  pageSize: 10,
})
const isShow = ref(1)
// 列表
const loading = ref(false)
const dataList = ref([])
const total = ref(0)
const getList = async () => {
  loading.value = true
  try {
    const res = await attendanceApi.reportList(queryParams)
    dataList.value = res.items
    total.value = res.totalCount
  } finally {
    loading.value = false
  }
}
// 详情
const detailRef = ref()
const handleDetail = (row) => {
  isShow.value = 2
  setTimeout(() => {
    detailRef.value.open(row)
  }, 100)
}
// 关闭子页面
const handleClose = () => {
  isShow.value = 1
}
// 搜索
const handleSearch = () => {
  queryParams.pageNo = 1
  getList()
}
onMounted(() => {
  getList()
})
</script>
<style lang="less" scoped></style>
