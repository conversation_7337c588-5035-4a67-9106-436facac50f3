<template>
  <div class="fanhui" v-if="titleName == '设置'">
    <el-button icon="el-icon-arrowleft" @click="fanhui">返回 </el-button>
  </div>
  <div class="center">
    <div class="top">
      <div>
        <div class="liti">
          <div class="xian"></div>
          <div class="juLeft">基础设置</div>
        </div>
        <el-form ref="formRef" label-width="180px" class="xinhao">
          <el-form-item label="所属月份:">
            <el-date-picker
              :disabled="isSendFlag ? false : true"
              v-model="queryParams.month"
              type="month"
              placeholder="请选择月份"
              value-format='YYYY-MM-DD'
            />
          </el-form-item>
          <el-form-item label="标题:">
            <el-input
              :disabled="isSendFlag ? false : true"
              v-model="queryParams.name"
              style="width: 240px"
              placeholder="请输入标题"
            />
          </el-form-item>
        </el-form>
      </div>
      <!--  -->
      <div>
        <div class="liti">
          <div class="xian"></div>
          <div class="juLeft">薪资项设置</div>
        </div>

        <el-form ref="formRef" label-width="180px" class="xinhao">
          <el-form-item label="实发金额:">
            <!-- :disabled="isSendFlag" -->
            <el-select
              v-model="queryParams.paySalaryItemId"
              placeholder="请选择"
              size="large"
              style="width: 240px"
              disabled
            >
              <el-option
                v-for="item in newHeadList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-tooltip
              class="box-item"
              effect="dark"
              content="实发金额是指员工实际到手的工资,将显示在员工工资条的顶部蓝色区域"
              placement="right"
            >
              <img src="@/assets/imgs/wenhao.svg" alt="" />
            </el-tooltip>
          </el-form-item>
        </el-form>
        <el-form ref="formRef" label-width="180px">
          <el-form-item label="隐藏空数据项:">
            <el-select
              v-model="queryParams.selectHides"
              placeholder="请选择"
              size="large"
              style="width: 240px"
              multiple
            >
              <el-option
                v-for="item in newHeadList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-tooltip
              class="box-item"
              effect="dark"
              content="在员工收到的工资条上,隐藏数值为空(或数值为0)的薪资项"
              placement="right"
            >
              <img src="@/assets/imgs/wenhao.svg" alt="" />
            </el-tooltip>
          </el-form-item>
        </el-form>
      </div>
      <!--  -->
      <div>
        <div class="liti">
          <div class="xian"></div>
          <div class="juLeft">功能设置</div>
        </div>

        <el-form ref="formRef" label-width="180px">
          <el-form-item label="阅后即焚:">
            <el-tooltip
              v-if="!isSendFlag"
              class="box-item"
              effect="dark"
              content="需撤回所有工资条后，才能编辑"
              placement="right"
            >
              <el-switch
                v-model="queryParams.matchFlag"
                :disabled="isSendFlag ? false : true"
                active-value="0"
                inactive-value="1"
              />
            </el-tooltip>

            <el-switch
              v-else
              v-model="queryParams.matchFlag"
              :disabled="isSendFlag ? false : true"
              active-value="0"
              inactive-value="1"
            />

            <el-tooltip
              class="box-item"
              effect="dark"
              content="在员工查看工资条一定时间后,将变为不可查阅"
              placement="right"
            >
              <img src="@/assets/imgs/wenhao.svg" alt="" />
            </el-tooltip>
          </el-form-item>
          <el-form-item v-if="queryParams.matchFlag == 0">
            员工查看
            <div class="shij">
              <el-input v-model="queryParams.matchTime" type="number" style="width: 80px" />
              <el-select v-model="matchUnit" placeholder="请选择" size="large" style="width: 90px">
                <el-option label="分钟" value="minute" />
                <el-option label="小时" value="hour" />
                <el-option label="天" value="day" />
              </el-select>
            </div>
            <span class="juli">后自动销毁</span>
          </el-form-item>

          <el-form-item label="员工反馈:">
            <el-tooltip
              v-if="!isSendFlag"
              class="box-item"
              effect="dark"
              content="需撤回所有工资条后，才能编辑"
              placement="right"
            >
              <el-switch
                v-model="queryParams.feedbackFalg"
                :disabled="isSendFlag ? false : true"
                active-value="0"
                inactive-value="1"
              />
            </el-tooltip>
            <el-switch
              v-else
              v-model="queryParams.feedbackFalg"
              :disabled="isSendFlag ? false : true"
              active-value="0"
              inactive-value="1"
            />

            <el-tooltip
              class="box-item"
              effect="dark"
              content="允许员工在工资条上反馈工资问题"
              placement="right"
            >
              <img src="@/assets/imgs/wenhao.svg" alt="" />
            </el-tooltip>
          </el-form-item>

          <el-form-item v-if="queryParams.feedbackFalg == 0">
            <div class="fankui">
              <div>答疑人员:</div>
              <div>{{ Uservalue && Uservalue.length > 0 ? Uservalue[0].name : '' }}</div>
              <div>
                <el-button type="primary" size="small" @click="$refs.orgPicker.show()"
                  >更换人员</el-button
                ></div
              >
            </div>
            <org-picker type="user" ref="orgPicker" :selected="Uservalue" @ok="selected" />
          </el-form-item>

          <el-form-item label="签名确认:">
            <el-tooltip
              v-if="!isSendFlag"
              class="box-item"
              effect="dark"
              content="需撤回所有工资条后，才能编辑"
              placement="right"
            >
              <el-switch
                v-model="queryParams.signFlag"
                :disabled="isSendFlag ? false : true"
                active-value="0"
                inactive-value="1"
              />
            </el-tooltip>

            <el-switch
              v-else
              v-model="queryParams.signFlag"
              :disabled="isSendFlag ? false : true"
              active-value="0"
              inactive-value="1"
            />

            <el-tooltip
              class="box-item"
              effect="dark"
              content="开启后,员工确认工资条时需要手写签名"
              placement="right"
            >
              <img src="@/assets/imgs/wenhao.svg" alt="" />
            </el-tooltip>
          </el-form-item>
          <el-form-item label="自动确认:">
            <el-tooltip
              v-if="!isSendFlag"
              class="box-item"
              effect="dark"
              content="需撤回所有工资条后，才能编辑"
              placement="right"
            >
              <el-switch
                v-model="queryParams.confirmFlag"
                :disabled="isSendFlag ? false : true"
                active-value="0"
                inactive-value="1"
              />
            </el-tooltip>

            <el-switch
              v-else
              v-model="queryParams.confirmFlag"
              :disabled="isSendFlag ? false : true"
              active-value="0"
              inactive-value="1"
            />

            <el-tooltip
              class="box-item"
              effect="dark"
              content="开启后,员工工资条会在设置时间后自动确认"
              placement="right"
            >
              <img src="@/assets/imgs/wenhao.svg" alt="" />
            </el-tooltip>
          </el-form-item>
          <el-form-item v-if="queryParams.confirmFlag == 0">
            员工查看
            <div class="shij">
              <el-input v-model="queryParams.confirmTime" type="number" style="width: 80px" />
              <el-select
                v-model="confirmUnit"
                placeholder="请选择"
                size="large"
                style="width: 90px"
              >
                <el-option label="分钟" value="minute" />
                <el-option label="小时" value="hour" />
                <el-option label="天" value="day" />
              </el-select>
            </div>
            <span class="juli">后自动确认</span>
          </el-form-item>
        </el-form>
      </div>
      <!--  -->
      <div>
        <div class="liti">
          <div class="xian"></div>
          <div class="juLeft">显示设置</div>
        </div>

        <el-form ref="formRef" label-width="180px">
          <el-form-item label="温馨提示:">
            <el-switch v-model="queryParams.warmTipFlag" active-value="0" inactive-value="1" />
          </el-form-item>
          <el-form-item v-if="queryParams.warmTipFlag == 0">
            <el-input v-model="queryParams.warmTip" style="width: 240px" type="textarea" />
          </el-form-item>
          <el-form-item label="员工关怀:">
            <el-switch v-model="queryParams.solicitudeFlag" active-value="0" inactive-value="1" />
          </el-form-item>
          <el-form-item v-if="queryParams.solicitudeFlag == 0">
            <el-input
              v-model="queryParams.solicitude"
              style="width: 240px"
              placeholder="请输入"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- <div class="preview-entry">
      <div class="background-img">
        <img
          class="moxin"
          src="https://img.alicdn.com/imgextra/i2/O1CN01NvskDE1gchaywYE8u_!!6000000004163-2-tps-406-840.png"
        />
      </div>

      <div class="preview">
        <div class="processBox">
          <div class="layer"></div>
          <div class="weitu">
            <img src="@/assets/imgs/weitu.png" alt="" />
            <div class="us">李思思，工作辛苦啦</div>
            <div class="us1">
              <div>8888</div>
              <div>实发金额</div>
            </div>
          </div>

          <div class="tishi">
            <div>温馨提示</div>
            <div>工资条属于敏感信息，请注意保密</div>
          </div>
          <div class="boxFor" v-for="(item, index) in billDataRespVOList" :key="index">
            <div>{{ item.itemName }}</div>
            <div>{{ item.itemValue }}</div>
          </div>
        </div>
      </div>
      <div class="fotr">
        <el-button class="auditBtn">对工资有疑问</el-button>
        <el-button type="primary" class="auditBtn" @click="nextStep">确认无误</el-button>
      </div>
    </div> -->
    <phoneModel :previewPhoneID="previewPhoneID" />
  </div>

  <div class="footerSubmit" :style="{ left: isCollapse ? '64px' : '200px' }">
    <div v-if="titleName == '设置'">
      <el-button type="primary" class="auditBtn" @click="completeSetup">完成设置</el-button>
    </div>
    <div v-else>
      <el-button class="auditBtn" @click="reUpload">重新上传</el-button>
      <el-button type="primary" class="auditBtn" @click="completeSetup">下一步</el-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { propTypes } from '@/utils/propTypes'
import { getUserProfile } from '@/api/system/user/profile'
import { Search, CaretBottom, ArrowRight } from '@element-plus/icons-vue'
import { tr } from 'element-plus/es/locale'
import { useAppStore } from '@/store/modules/app'
import * as payslipApi from '@/api/system/payslip'
import OrgPicker from '@/components/common/OrgPicker.vue'
import phoneModel from './phoneModel.vue'

import dayjs from 'dayjs'
import type { DropdownInstance, ElMessageBox } from 'element-plus'
defineOptions({ name: 'orgstructure' })
import { couldStartTrivia, factory } from 'typescript'
const props = defineProps({
  idVal: propTypes.string.def(''),
  titleName: propTypes.string.def(''),
  isSendFlag: propTypes.bool.def(true)
})
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const Uservalue = ref([])
const newHeadList = ref([])
const billDataRespVOList = ref([])
const userObj = ref({})
const previewPhoneID = ref()
const matchUnit = ref('minute')
const confirmUnit = ref('minute')
const queryParams = reactive({
  id: '',
  month: undefined,
  name: undefined,
  paySalaryItemId: undefined,
  selectHides: [],
  matchFlag: '1',
  feedbackFalg: '0',
  signFlag: '1',
  confirmFlag: '1',
  warmTip: '工资条属于敏感信息，请注意保密',
  solicitude: undefined,
  warmTipFlag: '0',
  solicitudeFlag: '0',
  matchTime: undefined,
  matchUnit: '',
  confirmTime: undefined,
  confirmUnit: ''
})

const emit = defineEmits(['success', 'reUpload', 'refreshTable']) // 定义 success 事件，用于操作成功后的回调

// 发送
const send = () => {}
// 完成设置 或 下一步
const completeSetup = () => {
  try {
    if (queryParams.matchFlag != '0') {
      queryParams.matchTime = undefined
      queryParams.matchUnit = ''
    } else {
      queryParams.matchUnit = matchUnit.value
    }

    if (queryParams.confirmFlag != '0') {
      queryParams.confirmTime = undefined
      queryParams.confirmUnit = ''
    } else {
      queryParams.confirmUnit = confirmUnit.value
    }
    queryParams.id = props.idVal
    payslipApi.updateSalaryBill(queryParams)
    message.success('操作成功')
    if (props.titleName == '设置') {
      emit('success')
      emit('refreshTable')
    } else {
      emit('refreshTable')
    }
  } finally {
  }
}
// 获取列表数据
const getList = async () => {
  const data = await payslipApi.getSalaryBill(props.idVal)
  previewPhoneID.value = data.previewId
  // previewSalaryBill(data.previewId)
  Uservalue.value.push({
    name: data.feedbackUserName,
    id: data.feedbackUserId + ''
  })
  console.log(data)
  queryParams.paySalaryItemId = data.paySalaryItemId
  // queryParams.month = data.month[0] + '-' + data.month[1]
  queryParams.month = data.month
  queryParams.name = data.name
  queryParams.matchFlag = data.matchFlag + ''
  queryParams.feedbackFalg = data.feedbackFalg + ''
  queryParams.signFlag = data.signFlag + ''
  queryParams.confirmFlag = data.confirmFlag + ''
  queryParams.warmTipFlag = data.warmTipFlag + ''
  queryParams.solicitudeFlag = data.solicitudeFlag + ''
  queryParams.selectHides = data.selectHides
  queryParams.matchTime = data.matchTime
  queryParams.warmTip = data.warmTip
  queryParams.solicitude = data.solicitude
  if (data.matchUnit) {
    matchUnit.value = data.matchUnit
  } else {
    matchUnit.value = matchUnit.value
  }
  queryParams.confirmTime = data.confirmTime
  if (data.confirmUnit) {
    confirmUnit.value = data.confirmUnit
  } else {
    confirmUnit.value = confirmUnit.value
  }
  let headList = JSON.parse(data.headList)
  // userObj.value = data
  console.log(headList)
  for (let key in headList) {
    if (key != '姓名' && key != '用户编号') {
      newHeadList.value.push({
        value: headList[key],
        label: key
      })
    }
  }
}
// 右侧手机模型预览数据
const previewSalaryBill = async (id) => {
  const data = await payslipApi.previewSalaryBill({ id })
  console.log(data)
  billDataRespVOList.value = data.billDataRespVOList
}
const reUpload = () => {
  emit('reUpload', '第三步')
}
// 返回
const fanhui = () => {
  if (props.titleName == '设置') {
    emit('success')
  }
}
// 选择更换答疑人员
const selected = (va) => {
  console.log(va)
  Uservalue.value = va.map((item, i) => {
    return {
      name: item.name,
      id: item.id
    }
  })
}
// 获取用户个人登录信息
// const getUserInfo = async () => {
//   const users = await getUserProfile()
//   userInfo.value = users
// }

// 监听折叠面板
const appStore = useAppStore()
const collapse = computed(() => appStore.getCollapse)
const isCollapse = computed(() => appStore.getCollapse)
watch(
  () => collapse.value,
  (newPath, oldPath) => {
    isCollapse.value = newPath
  }
)
// 监听浏览器宽度
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
}
watch( queryParams,(newV, oldV) => {
    console.log(newV,'newV');
    console.log(oldV,'oldV');
    
  }
)
/** 初始化 */
onMounted(() => {
  getList()
  window.addEventListener('resize', handleResize)
})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.center {
  display: flex;
  margin-top: 40px;
  justify-content: center;
}
.fanhui {
  // display: flex;
  // border-bottom: 1px solid #ccc;
  // padding: 10px 0;
  // span {
  //   margin: 0 auto;
  //   font-weight: bold;
  //   font-size: 14px;
  //   color: #303133;
  // }
}
:deep .el-tooltip__trigger {
  margin-left: 8px;
}
.top {
  width: 650px;
  // height: 1000px;
  background: #f9fafb;
  .liti {
    display: flex;
    align-items: center;
    padding-top: 10px;
    .xian {
      width: 3px;
      height: 16px;
      background: #3370ff;
    }
    .juLeft {
      margin-left: 8px;
    }
  }
}
:deep .xinhao .el-form-item--default .el-form-item__label::before {
  content: '* ';
  color: red;
}
.footerSubmit {
  // text-align: center;
  // margin-top: 110px;
  // padding: 5px 0;
  // box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
  // margin-bottom: -19px;
  position: fixed;
  bottom: 0;
  // left: 220px;
  right: 0;
  height: 60px;
  box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  z-index: 5;
}

.preview-entry {
  position: relative;
  // width: 203px;
  width: 302px;
  height: 100%;
  margin-left: 31px;
  margin-top: 30px;

  // width: 302px;
  // height: 584px;
  .background-img {
    position: absolute;
    width: 100%;
    z-index: 1;
    // height: 100%;
    pointer-events: none;
  }
  .moxin {
    width: 100%;
    height: 100%;
  }
}
.preview {
  background-color: #f6f6f6;
  border-radius: 0 0 42px 42px;
  overflow-y: scroll;
  // position: absolute;
  width: 100%;
  // height: 100%;
  height: 530px;
}
.processBox {
  position: relative;
  padding-top: 60px;
  padding-left: 15px;
}

.layer {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 12;
}
.weitu {
  margin: 40px 0 0 5px;
  position: relative;
}
.us {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translate(-50%);
  font-weight: 500;
  font-size: 10px;
  color: #ffffff;
}
.us1 {
  position: absolute;
  top: 52px;
  left: 50%;
  transform: translate(-50%);
  > div:nth-of-type(1) {
    font-weight: bold;
    font-size: 28px;
    color: #ffffff;
    text-align: center;
  }
  > div:nth-of-type(2) {
    font-weight: 500;
    font-size: 10px;
    color: #ffffff;
    text-align: center;
  }
}
.tishi {
  width: 242px;
  height: 62px;
  background: #fffbe6;
  border-radius: 6px;
  margin: 10px auto 0 auto;
  padding: 7px 0 0 8px;
  > div:nth-of-type(1) {
    font-weight: 400;
    font-size: 14px;
    color: #303133;
  }
  > div:nth-of-type(2) {
    font-weight: 400;
    font-size: 12px;
    color: #606266;
    margin-top: 8px;
  }
}
.boxFor {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  background: #fff;
  margin-top: 10px;
  height: 34px;
  line-height: 34px;
  > div {
    font-weight: 400;
    font-size: 12px;
    color: #606266;
  }
}
.titleP {
  margin: 0;
  padding: 0 0.625rem 0.625rem 1rem;
  color: #545456;
  font-size: 10px;
}
.fotr {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
.fankui {
  display: flex;
  align-items: center;
  height: 48px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #dcdfe6;

  > div:nth-of-type(1) {
    margin-left: 13px;
    font-weight: 400;
    color: #606266;
    font-size: 14px;
  }
  > div:nth-of-type(2) {
    margin-left: 5px;
    font-weight: 500;
    color: #303133;
    font-size: 14px;
  }
  > div:nth-of-type(3) {
    margin: 0 10px 0 90px;
  }
}
.shij {
  margin-left: 10px;
}
:deep.shij .el-select--large .el-select__wrapper {
  min-height: 32px;
}
.juli {
  margin-left: 10px;
}
</style>
