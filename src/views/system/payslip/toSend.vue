<template>
  <ContentWrap>
    <div class="top">
      <el-button icon="el-icon-arrowleft" @click="fanhui">返回 </el-button>
      <span>{{ titleName }}</span>
    </div>
    <div class="topCen">
      <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item label="员工姓名" prop="empName">
          <el-input
            v-model="queryParams.empName"
            class="!w-240px"
            placeholder="请输入姓名"
            clearable
          />
        </el-form-item>

        <el-form-item label="发送状态" prop="sendStatus">
          <el-select
            v-model="queryParams.sendStatus"
            placeholder="发送状态"
            clearable
            class="!w-240px"
          >
            <el-option label="已发送" value="0" />
            <el-option label="未发送" value="1" />
            <el-option label="已撤回" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="查看状态" prop="viewStatus">
          <el-select
            v-model="queryParams.viewStatus"
            placeholder="查看状态"
            clearable
            class="!w-240px"
          >
            <el-option label="已查看" value="0" />
            <el-option label="未查看" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="确认状态" prop="confirmStatus">
          <el-select
            v-model="queryParams.confirmStatus"
            placeholder="确认状态"
            clearable
            class="!w-240px"
          >
            <el-option label="已确认" value="0" />
            <el-option label="未确认" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"> <Icon icon="ep:search" />搜索 </el-button>
          <el-button @click="resetQuery"> <Icon icon="ep:refresh" />重置 </el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="allSend">全部发送</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- show-overflow-tooltip -->
    <el-table v-loading="loading" :data="dataList" @cell-click="handleCellClick">
      <el-table-column label="姓名" width="400px">
        <template #default="scope">
          <div class="taUser">
            <div class="taUserLeft">{{
              scope.row.empName.length >= 3 ? scope.row.empName.substring(1, 3) : scope.row.empName
            }}</div>
            <div class="taUser1">
              <div>{{ scope.row.empName }}</div>
              <div>{{ scope.row.deptNames }}</div>
            </div>
          </div>
          <!-- <span>{{
              scope.row.empName.length >= 3 ? scope.row.empName.substring(1, 3) : scope.row.empName
            }}</span>
            <span>{{ scope.row.empName }}</span>
            <br>
            <span>{{ scope.row.deptNames }}</span> -->
          <!-- 
          <div class="aaa">
            <span>{{
              scope.row.empName.length >= 3 ? scope.row.empName.substring(1, 3) : scope.row.empName
            }}</span>
            <div>
              <div>{{ scope.row.empName }}</div>
              <div>{{ scope.row.deptNames }}</div>
            </div>
           
          </div> -->
        </template>
      </el-table-column>

      <el-table-column label="员工状态">
        <template #default="scope">
          {{ scope.row.empStatus == 0 ? '在职' : '离职' }}
        </template>
      </el-table-column>
      <el-table-column label="实发金额" prop="paySalary" />
      <el-table-column label="发送状态">
        <template #default="scope">
          {{
            scope.row.sendStatus == 1
              ? '未发送'
              : scope.row.sendStatus == 0
              ? '已发送'
              : scope.row.sendStatus == 2
              ? '已撤回'
              : '-'
          }}
        </template>
      </el-table-column>
      <el-table-column label="查看状态">
        <template #default="scope">
          {{ scope.row.viewStatus == 0 ? '已查看' : '未查看' }}
        </template>
      </el-table-column>
      <el-table-column label="确认状态">
        <template #default="scope">
          {{ scope.row.confirmStatus == 0 ? '已确认' : '未确认' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="160">
        <template #default="scope">
          <div @click.stop>
            <el-button
              type="primary"
              v-if="scope.row.sendStatus == 1"
              link
              @click="send(scope.row.id)"
            >
              发送
            </el-button>
            <el-button
              type="danger"
              v-if="scope.row.sendStatus == 0"
              link
              @click="withdrawal(scope.row.id)"
            >
              撤回
            </el-button>

            <el-button
              type="primary"
              v-if="scope.row.sendStatus == 2"
              link
              @click="send(scope.row.id)"
            >
              重新发送
            </el-button>
            <el-button
              type="primary"
              v-if="scope.row.sendStatus == 1 || scope.row.sendStatus == 2"
              link
              @click="edit(scope.row.id)"
              v-hasPermi="['system:salary-bill-user:update']"
            >
              编辑
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="fenye">
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </ContentWrap>
  <div class="bianji">
    <el-drawer v-model="drawer" title="薪资明细" size="500px">
      <!-- title="I am the title"  -->
      <!-- <template #header>
      <h4>set title by slot</h4>
    </template> -->
      <div class="biaodan">
        <el-form class="-mb-15px" :model="biaoDanParams" ref="biaoDanFormRef" label-width="100px">
          <el-form-item
            :label="item.itemName"
            prop="nickname"
            v-for="(item, index) in billDataRespVOList"
            :key="index"
          >
            <el-input v-model="item.itemValue" clearable placeholder="请输入" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="drawer = false">取消</el-button>
          <el-button type="primary" @click="allConfirmed"> 确定 </el-button>
        </div>
      </template>
    </el-drawer>
  </div>

  <!-- 手机模型预览 -->
  <el-drawer v-model="previewPhoneDrawer" title="预览" size="500px">
    <!-- title="I am the title"  -->
    <!-- <template #header>
      <h4>set title by slot</h4>
    </template> -->
    <phoneModel :previewPhoneID="previewPhoneID" />
  </el-drawer>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { propTypes } from '@/utils/propTypes'
import { getUserProfile } from '@/api/system/user/profile'
import { Search, CaretBottom, ArrowRight } from '@element-plus/icons-vue'
import { tr } from 'element-plus/es/locale'
import { useAppStore } from '@/store/modules/app'
import * as payslipApi from '@/api/system/payslip'
import phoneModel from './phoneModel.vue'

import dayjs from 'dayjs'
import type { DropdownInstance, ElMessageBox } from 'element-plus'
defineOptions({ name: 'orgstructure' })
import { couldStartTrivia, factory } from 'typescript'
import { emit } from 'process'

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const props = defineProps({
  idVal: propTypes.string.def(''),
  titleName: propTypes.string.def('')
})
const loading = ref(false) // 列表的加载中
const drawer = ref(false)
const previewPhoneDrawer = ref(false)
const queryFormRef = ref() // 搜索的表单
const total = ref(0)
const previewPhoneID = ref()
const dataList = ref([])
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  empName: undefined,
  sendStatus: undefined,
  viewStatus: undefined,
  confirmStatus: undefined,
  // id: ''
  salaryBillId: ''
})
// 编辑表单数据
const biaoDanParams = reactive({
  empName: undefined,
  sendStatus: undefined,
  viewStatus: undefined,
  confirmStatus: undefined
})

const billDataRespVOList = ref([])

const formData = reactive({})

const emit = defineEmits(['success', 'fanhui']) // 定义 success 事件，用于操作成功后的回调
// 返回
const fanhui = () => {
  emit('success')
  emit('fanhui')
}
// 获取列表数据
const getList = async () => {
  try {
    console.log(props.titleName)
    loading.value = true
    queryParams.salaryBillId = props.idVal
    const data = await payslipApi.salaryBillUser(queryParams)
    dataList.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}
// 全部发送
const allSend = async () => {
  let obj = {
    // id:dataList.value.map(item=> item.id).join(','),
    // salaryBillId: dataList.value.map((item) => item.salaryBillId).join(',')
    salaryBillId: props.idVal
  }
  console.log(obj)
  await payslipApi.sendAllSalaryBill(obj)
  getList()
  message.success('操作成功')
}

// 可撤回
const withdrawal = (id) => {
  try {
    ElMessageBox.confirm('系统将撤回该员工当月工资条', '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        const data = await payslipApi.revokeSalaryBill({ id })
        message.success('操作成功')
        getList()
      })
      .catch(() => {})
    getList()
  } finally {
  }
}

const handleCellClick = async (row) => {
  console.log(row)
  previewPhoneID.value = row.id
  previewPhoneDrawer.value = true
}

// 发送
const send = (id) => {
  ElMessageBox.confirm('工资条将通过办办工作通知发送给员工', '系统提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      await payslipApi.sendSalaryBill({ id })
      message.success('操作成功')
      getList()
    })
    .catch(() => {})
}
// 编辑
const edit = async (id) => {
  const data = await payslipApi.previewSalaryBill({ id })
  console.log(data)
  billDataRespVOList.value = data.billDataRespVOList
  drawer.value = true
}
// 编辑确定
const allConfirmed = async () => {
  console.log(billDataRespVOList.value[0])
  billDataRespVOList.value.map((item) => delete item.hidden)
  try {
    // const data = await payslipApi.editSalaryBill({ salaryBillDataDOs: billDataRespVOList.value })
    const data = await payslipApi.editSalaryBill(billDataRespVOList.value)
    getList()
    message.success(t('common.updateSuccess'))
    drawer.value = false
  } finally {
  }
}

// 获取用户个人登录信息
// const getUserInfo = async () => {
//   const users = await getUserProfile()
//   userInfo.value = users
// }

// 监听折叠面板
const appStore = useAppStore()
const collapse = computed(() => appStore.getCollapse)
const isCollapse = computed(() => appStore.getCollapse)
watch(
  () => collapse.value,
  (newPath, oldPath) => {
    isCollapse.value = newPath
  }
)
// 监听浏览器宽度
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

/** 初始化 */
onMounted(() => {
  getList()
  window.addEventListener('resize', handleResize)
})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.top {
  display: flex;
  span {
    margin: 0 auto;
    font-weight: bold;
    font-size: 14px;
    color: #303133;
  }
}
.topCen {
  margin: 30px 0;
}
.taUser {
  display: flex;
  align-items: center;
  .taUserLeft {
    width: 40px;
    height: 40px;
    background: #3370ff;
    border-radius: 40px;
    line-height: 40px;
    font-weight: 500;
    font-size: 12px;
    color: #ffffff;
    margin-right: 10px;
    text-align: center;
  }
  .taUser1 {
    > div:nth-of-type(1) {
      font-weight: 500;
      font-size: 14px;
      color: #303133;
    }
    > div:nth-of-type(2) {
      font-weight: 400;
      font-size: 14px;
      color: #909399;
    }
  }
}
.fenye .el-pagination {
  display: flex !important;
}
</style>
