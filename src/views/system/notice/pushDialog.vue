<template>
  <Dialog v-model="dialogVisible" title="推送公告" width="700">
    <el-form ref="formRef" label-width="140px">
      <el-form-item label="推送：">
        <el-button type="primary" @click="handleCheck('1')">推送部门/人员</el-button>
        <div class="tagDiv">
          <el-tag size="small" closable v-for="(item, i) in pushUser" :key="i" @close="handleDel(i, '2')">
            {{ item.name }}
          </el-tag>
        </div>
      </el-form-item>
      <el-form-item label="是否到期提醒：">
        <div class="checkContent" @click="checkTime">
          <img :src="remindFlag == 1 ? CheckedImg : defaultImg" />
          <span>{{ remindFlag == 1 ? remindDate + ' 提醒' : '到期提醒' }}</span>
          <el-date-picker v-model="remindDate" type="datetime" value-format="YYYY-MM-DD HH:mm" time-format="HH:mm"
            placeholder="请选择" ref="dataRef" class="date-picker" @visible-change="visibleChange" />
        </div>
      </el-form-item>
      <el-form-item label="提醒：" v-if="remindFlag == 1">
        <el-button type="primary" @click="handleCheck('2')">提醒部门/人员</el-button>
        <div class="tagDiv">
          <el-tag size="small" closable v-for="(item, i) in tipUser" :key="i" @close="handleDel(i, '2')">
            {{ item.name }}
          </el-tag>
        </div>
      </el-form-item>
      <el-form-item label="是否定时发送：">
        <div class="checkContent" @click="checkTime2">
          <img :src="timingSendFlag == 1 ? CheckedImg : defaultImg" />
          <span>{{ timingSendFlag == 1 ? timingSendDate + ' 发送' : '定时发送' }}</span>
          <el-date-picker v-model="timingSendDate" type="datetime" value-format="YYYY-MM-DD HH:mm" time-format="HH:mm"
            placeholder="请选择" ref="dataRef2" class="date-picker" @visible-change="visibleChange2" />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitAll">推 送</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
  <org-picker :title="orgType == 1 ? '选择要推送的部门/人员' : '选择要提醒的部门/人员'" ref="orgPicker" multiple
    :selected="orgType == 1 ? pushUser : tipUser" @ok="selected" />

</template>
<script lang="ts" setup>
import * as NoticeApi from '@/api/system/notice'
import OrgPicker from '@/components/common/OrgPicker.vue'
import defaultImg from '@/assets/image/default.png'
import CheckedImg from '@/assets/image/checked.png'
const { t } = useI18n()
const message = useMessage()

const dialogVisible = ref(false)
const formLoading = ref(false)

const pushUser = ref<any[]>([])//推送列表
const tipUser = ref<any[]>([])//提醒列表
const orgType = ref(1)//选择类型
const remindFlag = ref(0)//提醒下拉类型 0否1是
const pushId = ref('')
const remindDate = ref('')

const timingSendFlag = ref(0)//是否到期提醒 0否1是
const timingSendDate = ref('')
/** 打开弹窗 */
const open = async (id: string) => {
  pushId.value = id
  pushUser.value = []
  tipUser.value = []
  remindDate.value = ''
  remindFlag.value = 0
  timingSendFlag.value = 0
  timingSendDate.value = ''
  dialogVisible.value = true

}


const orgPicker = ref()
const handleCheck = (type) => {
  orgType.value = type
  orgPicker.value.show()
}
const selected = (users: any) => {
  if (orgType.value == 1) {
    pushUser.value = users
  }
  if (orgType.value == 2) {
    tipUser.value = users
  }
}
const handleDel = (i, type) => {
  if (type == 1) {
    pushUser.value.splice(i, 1)
  }
  if (type == 2) {
    tipUser.value.splice(i, 1)
  }
}

// 到期提醒
const dataRef = ref()
const checkTime = () => {
  remindFlag.value = remindFlag.value == 0 ? 1 : 0
  if (remindFlag.value == 1 && dataRef.value) {
    remindDate.value = ''
    dataRef.value.focus()
  }
}
const visibleChange = (event) => {
  if (!event && !remindDate.value) {
    remindFlag.value = 0
  }
}
// 定时发送
const dataRef2 = ref()
const checkTime2 = () => {
  timingSendFlag.value = timingSendFlag.value == 0 ? 1 : 0
  if (timingSendFlag.value == 1 && dataRef2.value) {
    timingSendDate.value = ''
    dataRef2.value.focus()
  }
}
const visibleChange2 = (event) => {
  if (!event && !timingSendDate.value) {
    timingSendFlag.value = 0
  }
}



const submitAll = async () => {
  if (pushUser.value.length == 0) {
    return message.warning('请选择要推送的部门/人员')
  }
  if (remindFlag.value == 1 && (tipUser.value.length == 0 || !remindDate.value)) {
    return message.warning('请选择要提醒的时间/人员')
  }
  formLoading.value = true
  let obj = {
    sendUsers: pushUser.value.map(item => ({
      id: item.id,
      type: item.type
    })),
    remindFlag: remindFlag.value,
    remindDate: remindDate.value,
    remindUsers: tipUser.value.map(item => ({
      id: item.id,
      type: item.type
    })),
    timingSendFlag: timingSendFlag.value,
    timingSendDate: timingSendDate.value
  }
  try {
    await message.confirm('是否推送所选中通知？')
    await NoticeApi.pushNotice2(pushId.value, obj)
    message.success('推送成功')
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open })
const emit = defineEmits(['success'])

</script>
<style lang="less" scoped>
.el-select,
:deep(.el-date-editor) {
  width: 50%;
}

:deep(.el-form-item__content) {
  display: inline;
}

.tagDiv {
  margin-top: 5px;

  .el-tag {
    margin-right: 5px;
  }
}

.checkContent {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  line-height: normal;
  width: 50%;
  box-sizing: border-box;
  position: relative;

  img {
    margin-right: 6px;
  }

  :deep(.date-picker) {
    position: absolute;
    opacity: 0;
    pointer-events: none;
  }
}
</style>
