<template>
  <!-- 列表 -->
  <ContentWrap v-if="flag == 0">
    <h4>批量开具罚单</h4>
    <!-- 搜索工作栏 -->
    <el-form ref="queryFormRef" :inline="true" :model="queryParams" label-width="68px">
      <el-form-item label="企业名称" prop="companyName">
        <el-input class="!w-220px" v-model="queryParams.companyName" placeholder="请输入企业名称" />
      </el-form-item>
      <el-form-item label="被罚部门" prop="deptName">
        <el-input class="!w-220px" v-model="queryParams.deptName" placeholder="请输入部门名称" />
      </el-form-item>
      <el-form-item label="发起人" prop="creator">
        <el-input class="!w-220px" v-model="queryParams.creator" placeholder="请输入发起人" />
      </el-form-item>
      <el-form-item label="状态" prop="ticketStatus">
        <el-select v-model="queryParams.ticketStatus" placeholder="状态" clearable class="!w-240px">
          <el-option
            v-for="dict in ticketStatusList"
            :key="dict.id"
            :label="dict.textName"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发起日期" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="被罚原因" prop="reason">
        <el-input class="!w-220px" v-model="queryParams.reason" placeholder="请输入被罚原因" />
      </el-form-item>
       <el-form-item label="被罚人员" prop="userName">
        <el-input class="!w-220px" v-model="queryParams.userName" placeholder="请输入被罚人员" />
      </el-form-item>
      <div>
        <el-button @click="handleQuery"> <Icon icon="ep:search" />搜索 </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" />重置 </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" />导出
        </el-button>
      </div>
    </el-form>
    <div class="center">
      <div class="payroll" @click="upTick(1)">
        <el-icon style="margin-right: 1px"><Plus /></el-icon>上传表格
      </div>
      <div class="dier">罚单数量：{{ sumObj.total }}</div>
      <div class="dier">罚单金额(元)：{{ sumObj.total_money }}</div>
      <div class="dier">扣款分值：{{ sumObj.total_score }}</div>
    </div>

    <el-table v-loading="loading" :data="list">
      <el-table-column type="index" align="center" width="70" label="序号">
        <template #default="{ $index }">
          {{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="企业名称" prop="companyName" />
      <el-table-column align="center" label="审批单号" prop="instanceId" width="240" />
      <el-table-column align="center" label="发起人" prop="creator" />
      <el-table-column
        align="center"
        label="发起时间"
        prop="createTime"
        :formatter="dateFormatter"
        width="180"
      />
      <el-table-column align="center" label="被罚部门" prop="deptName" />
      <el-table-column align="center" label="被罚人员" prop="userName"> </el-table-column>
      <el-table-column align="center" label="罚单金额" prop="money" />
      <el-table-column align="center" label="扣款分值" prop="score" />
      <el-table-column align="center" label="扣款/扣分原因" prop="reason" />
      <el-table-column align="center" label="罚单日期" prop="ticketDate" width="120" />
      <el-table-column align="center" label="状态" prop="ticketStatus">
        <template #default="scope">
          <el-tag size="large" type="danger" v-if="scope.row.ticketStatus == 0">
            失败
          </el-tag>
          <el-tag size="large" type="primary" v-else-if="scope.row.ticketStatus == 1">
            成功
          </el-tag>
          <el-tag size="large" type="info" v-else-if="scope.row.ticketStatus == 2">
            未开
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="开具方式">
        <template #default="scope">
          {{ scope.row.type == 0 ? '手动开具' : scope.row.type == 1 ? '系统自动开具' : '未知方式' }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="失败原因" prop="badReason" />
      <el-table-column label="操作" min-width="150px">
        <template #default="scope">
          <el-button link type="primary" @click="viewDetails(scope.row.id)"> 详情 </el-button>
          <el-button
            v-if="scope.row.ticketStatus == 0 || scope.row.ticketStatus == 2"
            link
            type="primary"
            @click="resend(scope.row.id)"
          >
            {{scope.row.ticketStatus == 0 ? '重新发送' : '开具'}}
          </el-button>
          <!-- v-if="scope.row.relatedInstanceId" -->
          <el-button
            v-if="scope.row.relatedInstanceId"
            link
            type="primary"
            @click="showProcess(scope.row)"
          >
            关联流程详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <!--  -->
  <Dialog v-model="dialogVisible" title="上传罚单表格" width="800">
    <div class="suo" v-loading="formUpLoading">
      <div class="uploadCenter">
        <el-upload
          ref="upload"
          :auto-upload="false"
          class="upload-demo"
          :on-exceed="handleExceed"
          :before-upload="onBeforeUpload"
          :on-change="uploadFile"
          :limit="1"
          drag
        >
          <img src="@/assets/imgs/exce.svg" alt="" />
          <div class="el-upload__text">
            <div class="uploadPrompt">直接上传你现有的Excel罚单表格</div>
            <el-button type="primary" style="margin-top: 16px"> 选择罚单表格 </el-button>
          </div>
          <div @click.stop>
            <el-button link type="primary" style="margin-top: 16px" @click="xiazaishilie">
              下载示例模板
            </el-button>
          </div>
        </el-upload>
      </div>
      <div v-loading="excelLoading" v-if="excelSrc" style="height:calc(30vh)"
           element-loading-text="文件预览拼命加载中..." >
        <vue-office-excel :options="options" :src="excelSrc"
                           @rendered="renderedHandler" @error="errorHandler"/>
      </div>
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button :disabled="formUpLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>

  <!-- 详情/重新发送 -->
  <Dialog v-model="detailsVisible" title="详情">
    <!-- 详情 -->
    <el-form ref="formRef" :model="formData" label-width="100px" v-if="isSend">
      <el-form-item label="企业名称" prop="companyName">
        <el-input v-model="formData.companyName" disabled />
      </el-form-item>
      <el-form-item label="罚单日期" prop="ticketDate" class="bianji">
        <el-date-picker
          v-model="formData.ticketDate"
          value-format="YYYY-MM-DD "
          type="date"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-240px"
          disabled
        />
      </el-form-item>
      <el-form-item label="违规人员部门" prop="deptName">
        <el-input v-model="formData.deptName" disabled />
      </el-form-item>
      <el-form-item label="违规人员姓名" prop="userName">
        <el-input v-model="formData.userName" disabled />
      </el-form-item>
      <el-form-item label="扣款/扣分原因" prop="reason">
        <el-input v-model="formData.reason" disabled />
      </el-form-item>
      <el-form-item label="扣款金额(元)" prop="money">
        <el-input v-model="formData.money" disabled />
      </el-form-item>
      <el-form-item label="扣款分值" prop="score">
        <el-input v-model="formData.score" disabled />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" disabled type="textarea" />
      </el-form-item>
    </el-form>
    <!-- 重新发送 -->
    <el-form ref="formRef" v-loading="formLoading" :model="formData" label-width="100px" v-else>
      <el-form-item label="企业名称" prop="companyName">
        <el-input v-model="formData.companyName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="罚单日期" prop="ticketDate" class="bianji">
        <el-date-picker
          v-model="formData.ticketDate"
          value-format="YYYY-MM-DD "
          type="date"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-240px"
          :clearable="false"
        />
      </el-form-item>
      <el-form-item label="违规人员部门" prop="deptName">
        <el-input v-model="formData.deptName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="违规人员姓名" prop="userName">
        <el-input v-model="formData.userName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="扣款/扣分原因" prop="reason">
        <el-input v-model="formData.reason" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="扣款金额(元)" prop="money">
        <el-input v-model="formData.money" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="扣款分值" prop="score">
        <el-input v-model="formData.score" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer v-if="!isSend">
      <el-button @click="detailsVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="determineSend">确 定</el-button>
    </template>
  </Dialog>

  <el-drawer
    :size="isMobile ? '100%' : '560px'"
    direction="rtl"
    title="审批详情"
    v-model="processVisible"
    class="custom-detail-header"
  >
    <instance-preview
      v-if="processVisible"
      :instance-id="selectInstance.relatedInstanceId"
      :noId="selectInstance.nodeId"
      @handler-after="handlerAfter"
    ></instance-preview>
  </el-drawer>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'

import OrgPicker from '@/components/common/OrgPicker.vue'
import { Plus, CaretBottom, ArrowRight } from '@element-plus/icons-vue'

import { defaultProps, handleTree } from '@/utils/tree'
import { Icon } from '@/components/Icon'
import download from '@/utils/download'
// import InstancePreview from '../approval/ProcessInstancePreview.vue'
import InstancePreview from '@/views/wflow/workspace/approval/ProcessInstancePreview.vue'

import * as ticketApi from '@/api/system/penaltyTicket'

//引入VueOfficeExcel组件
import VueOfficeExcel from '@vue-office/excel'
//引入相关样式
import '@vue-office/excel/lib/index.css'
defineOptions({ name: 'SystemRoleAssignMenuForm' })
// 新的
import { genFileId } from 'element-plus'
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import dayjs from 'dayjs'

import { fa, tr } from 'element-plus/es/locale'
import { log } from 'console'
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const dialogVisible = ref(false)
const detailsVisible = ref(false)
const formUpLoading = ref(false)
const isSend = ref(false)
const processVisible = ref(false)
const selectInstance = ref({})
const isMobile = computed(() => window.screen.width < 450)
const fileList = ref([])
const detailsList = ref([])
const sumObj = ref({})
const scheduleTypeValue = ref('')
const ticketStatusList = ref([
  {
  id:0,
  textName:'失败'
},
  {
  id:1,
  textName:'成功'
},
  {
    id:2,
    textName:'未开'
  },
])
const scheduleType = ref([
  {
    value: '1',
    label: '会议'
  },
  {
    value: '2',
    label: '活动'
  }
])
const upload = ref<UploadInstance>()
const formData = ref({
  companyName: '',
  ticketDate: '',
  deptName: '',
  userName: '',
  reason: '',
  money: '',
  score: '',
  remark: ''
})
const loading = ref(false) // 列表的加载中
const flag = ref(0)
const list = ref([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryFormRef = ref() // 搜索的表单
const total1 = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  companyName: '',
  deptName: '',
  reason: '',
  userName: '',
  creator: '',
  createTime: [],
  ticketStatus:''
})
const detailsParams = reactive({
  pageNo: 1,
  pageSize: 10,
  departmentCalendarEventId: ''
})
const importParams = reactive({
  file: ''
})
const options = ref({
  xls: false,       //预览xlsx文件设为false；预览xls文件设为true
  minColLength: 0,  // excel最少渲染多少列，如果想实现xlsx文件内容有几列，就渲染几列，可以将此值设置为0.
  minRowLength: 0,  // excel最少渲染多少行，如果想实现根据xlsx实际函数渲染，可以将此值设置为0.
  widthOffset: 10,  //如果渲染出来的结果感觉单元格宽度不够，可以在默认渲染的列表宽度上再加 Npx宽
  heightOffset: 10, //在默认渲染的列表高度上再加 Npx高
  beforeTransformData: (workbookData) => {return workbookData}, //底层通过exceljs获取excel文件内容，通过该钩子函数，可以对获取的excel文件内容进行修改，比如某个单元格的数据显示不正确，可以在此自行修改每个单元格的value值。
  transformData: (workbookData) => {return workbookData}, //将获取到的excel数据进行处理之后且渲染到页面之前，可通过transformData对即将渲染的数据及样式进行修改，此时每个单元格的text值就是即将渲染到页面上的内容
})
const excelLoading = ref(false)

const excelSrc = ref()
const renderedHandler = ()=> {
  excelLoading.value = false
  console.log("渲染完成")
}
const errorHandler = () => {
  excelLoading.value = false
  message.error('文件预览渲染失败，请检查文件格式是否正确或文件是否已损坏')
  excelSrc.value = ''
  console.log("渲染失败")
}

watch(dialogVisible, (newValue, oldValue) => {
      if(newValue === false){
        excelSrc.value = ''
      }
    },
    {deep: true}
)
// 上传工资表
const upTick = () => {
  importParams.file = ''
  dialogVisible.value = true
}

/** 查询列表数据 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ticketApi.previewViolationTicket(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 查询罚单数量/罚单金额/扣款分值数量*/
const getSumData = async () => {
  // loading.value = true
  try {
    const data = await ticketApi.sumViolationTicket(queryParams)
    sumObj.value = data
  } finally {
    // loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
  getSumData()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}
/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ticketApi.exportExcelTicket(queryParams)
    download.excel(data, '批量开具罚单数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
// 下载示例罚单表
const xiazaishilie = async () => {
  try {
    // loading.value = true
    const data = await ticketApi.getImportTemplate()
    download.excel(data, '示例罚单表模板.xls')
  } finally {
    // loading.value = false
  }
}
const handleExceed: UploadProps['onExceed'] = (files) => {
  // console.log(files);
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}

const onBeforeUpload = (rawFile) => {
  // console.log(rawFile,'filesfiles');
}
const uploadFile = async (rawFile) => {
  const extension = rawFile.name.split('.').pop();
  if(extension === 'xlsx'){
    options.value.xls = false
  }else{
    options.value.xls = true
  }
  let reader = new FileReader();
  reader.readAsArrayBuffer(rawFile.raw);
  reader.onload = (loadEvent) => {
    let arrayBuffer = loadEvent.target.result;
    excelSrc.value = arrayBuffer
    excelLoading.value = true
  };
  importParams.file = rawFile.raw

  return
  // try {
  //   let form = new FormData()
  //   form.append('file', rawFile.raw)
  //   const data = await ticketApi.importViolationTicket(form)
  //   message.success('上传成功')
  //   console.log(data)
  // } finally {
  //   loading.value = false
  // }
}

const submitForm = async () => {
  formUpLoading.value = true
  let form = new FormData()
  form.append('file', importParams.file)
  try {
    const data = await ticketApi.importViolationTicket(form)
    console.log(data)
    dialogVisible.value = false
    formUpLoading.value = false
    if (data.code != 0) return message.error(data.msg)
    handleQuery()
    message.success('上传成功')
  } finally {
    loading.value = false
    formUpLoading.value = false
  }
}
// 详情
const viewDetails = async (id) => {
  isSend.value = true
  detailsVisible.value = true
  try {
    // const data = await RoleApi.selectDepartmentEventUserConfirmList(detailsParams)
    // const data = await ticketApi.delViolationTicket({ id })
    // console.log(data)
    // detailsList.value = data
    // total1.value = data.total
    formData.value = await ticketApi.delViolationTicket({ id })
  } finally {
  }
}
const showProcess = async (row) => {
  processVisible.value = true
  selectInstance.value = row
}

// 重新发送
const resend = async (id) => {
  formData.value.id = id
  isSend.value = false
  detailsVisible.value = true
  try {
    // const data = await RoleApi.selectDepartmentEventUserConfirmList(detailsParams)
    // const data = await ticketApi.delViolationTicket({ id })
    // console.log(data)
    // detailsList.value = data
    // total1.value = data.total
    formData.value = await ticketApi.delViolationTicket({ id })
  } finally {
  }
}

const determineSend = async () => {
  try {
    formLoading.value = true
    console.log(formData.value)
    const data = await ticketApi.resendViolationTicket(formData.value)
    console.log(data)
    formLoading.value = false
    detailsVisible.value = false
    message.success('发送成功')
    handleQuery()
  } finally {
    detailsVisible.value = false
    formLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
  getSumData()
})
</script>
<style lang="scss" scoped>
.center {
  display: flex;
  margin: 20px 0;
  .payroll {
    width: 98px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #3370ff;
    color: #ffffff;
    line-height: 32px;
    margin-bottom: 15px;
    cursor: pointer;
    border-radius: 5px;
    font-size: 14px;
    margin-right: 20px;
  }
  .dier {
    margin: 6px 20px 0 0;
  }
}
</style>
<style lang="less">
.bianji .el-date-editor.el-input,
.bianji.el-date-editor.el-input__wrapper {
  width: 100% !important;
}
</style>