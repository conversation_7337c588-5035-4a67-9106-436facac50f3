<template>
  <!-- 个人资料 -->
  <el-card class="oaCard" v-loading="loading">
    <el-page-header :icon="ArrowLeft" class="custom-page-header">
      <template #content>
        <span class="text-large font-600 mr-3">{{ userName }}个人档案</span>
      </template>
    </el-page-header>
    <div class="topDiv">
      <avatar :size="70" :name="userName" :src="avatar" class="myAvatar" />
      <p class="nameP">
        <span class="nameSpan">{{ userName }}</span>
      </p>
      <div class="topBtn">
        <el-button type="primary" @click="handleEdit" v-if="listData.length > 0 && isShow == 1">编辑</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="isShow == 2">保存</el-button>
        <el-button @click="handelReset" v-if="isShow == 2">取消</el-button>
      </div>
    </div>
    <div v-if="isShow == 1">
      <div v-for="(item, index) in listData" :key="index">
        <div class="form-title">
          <span class="title">{{ item.groupName }}</span>
          <div class="line"></div>
        </div>
        <div class="cBox">
          <div v-for="(child, i) in item.customFieldList" :key="i" class="forDiv">
            <span class="span1">{{ child.fieldName }}</span>
            <div class="image-comment" v-if="child.fieldType == 'ImageUpload'">
              <el-image class="image" preview-teleported :src="img"
                v-for="(img, i) in filterImages(child.customDataRespVO?.fieldValue ? (JSON.parse(child.customDataRespVO.fieldValue)) : [])"
                :key="i" :initialIndex="i"
                :preview-src-list="filterImages(child.customDataRespVO?.fieldValue ? (JSON.parse(child.customDataRespVO.fieldValue)) : [])" />
            </div>
            <div class="file-comment" v-else-if="child.fieldType == 'FileUpload'">
              <ellipsis class="file-item" type="primary" @click="download(file)" :content="file.name" :title="file.name"
                v-for="file in filterFiles(child.customDataRespVO?.fieldValue ? (JSON.parse(child.customDataRespVO.fieldValue)) : [])"
                :key="file.id">
                <template #pre>
                  <icon name="el-icon-document" />
                </template>
              </ellipsis>
            </div>
            <span v-else-if="child.fieldType == 'jobMultiple'">{{ child.customDataRespVO?.fieldValue ?
              toParse(child.customDataRespVO.fieldValue, 'jobMultiple') : '' }}</span>
            <span v-else-if="child.fieldType == 'DeptPicker'">{{ child.customDataRespVO?.fieldValue ?
              toParse(child.customDataRespVO.fieldValue, 'DeptPicker') : '' }}</span>
            <span class="span2" v-else>
              {{ child.customDataRespVO?.fieldValue }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="content-entry" v-show="isShow == 2">
      <div v-for="(item, index) in subData" :key="index" class="boxFor">
        <p class="titleP">{{ item.groupName }}</p>
        <div>
          <form-render mode="PC" class="process-form" :ref="'formRef' + index" :forms="getForms(item.customFieldList)"
            v-model="item.formData" :position="'left'" />
        </div>
      </div>
      <!-- <el-button @click="handleSubmit" class="btnClass" type="primary">提 交</el-button> -->
    </div>
  </el-card>
  <!-- 确认到岗 -->
  <confirmPost ref="confirmRef" @success="closeChild" />
</template>

<script>
import { UserRosterApi } from '@/api/hrManage/userRoster'
import FormRender from "@/views/wflow/common/form/FormRender.vue";
import confirmPost from '../empManage/confirmPost.vue'
import * as PostApi from '@/api/system/post'
import * as DeptApi from '@/api/system/dept'
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()
export default {
  name: "entryRegister",
  components: {
    FormRender,
    confirmPost
  },
  props: {
    from: {
      type: String,
      default: 'userRoster'//默认花名册模块进入
    },
    isPush: {
      type: Boolean,
      default: false//是否是入职完善推送的模式
    }
  },
  data() {
    return {
      isShow: 1,
      loading: false,
      propId: '',
      propsObj: {},
      listData: [],
      subData: [],
      postList: [],
      deptList: [],
      otherList: []
    };
  },
  computed: {
    userName() {
      return userStore.user.nickname ? userStore.user.nickname : 'Admin';
    },
    avatar() {
      return userStore.user.avatar ? userStore.user.avatar : '';
    }
  },
  created() {
    this.open()
  },
  methods: {
    open(e) {
      this.loading = true
      this.getUserInfo()
      this.getPostList()//职位下拉
      this.getDeptList()
    },
    closeChild() {
      this.$emit('close')
    },
    deepClone(obj) {
      if (obj === null || typeof obj !== 'object') return obj;
      if (Array.isArray(obj)) return obj.map(this.deepClone);
      const newObj = {};
      for (let key in obj) {
        newObj[key] = this.deepClone(obj[key]);
      }
      return newObj;
    },
    async getUserInfo() {
      try {
        let res = {}
        res = await UserRosterApi.getPersonRoster()
        this.listData = this.deepClone(res).customGroupList.filter(item => {
          item.customFieldList = item.customFieldList.filter(child => child.hiddenFromEmployeeFlag == 0 && child.fieldCode != 'emp_type')
          return item.customFieldList.length > 0 && item.groupName != '其它信息'
        })

        let newArr = []
        newArr = this.deepClone(res).customGroupList
        newArr.forEach((item) => {
          item.customFieldList.forEach((c) => {
            if (c.customDataRespVO) {
              const { id, instanceId, fieldId, fieldValue } = c.customDataRespVO;
              const { fieldCode, fieldName, fieldType } = c;

              if ((c.editFromEmployeeFlag !== 0 && fieldCode !== 'emp_type') || fieldCode === 'emp_type') {
                this.otherList.push({
                  id: id || null,
                  instanceId: instanceId || null,
                  fieldId: fieldId,
                  fieldKey: fieldCode,
                  fieldValue: fieldValue,
                  fieldName: fieldName,
                  fieldType: fieldType,
                  isJson: true
                });
              }
            }
          })
        })
        console.log(this.otherList, 'this.otherList')
        this.subData = this.deepClone(res).customGroupList.filter(item => {
          item.customFieldList = item.customFieldList.filter(child => child.editFromEmployeeFlag == 0 && child.fieldCode != 'emp_type')
          return item.customFieldList.length > 0 && item.groupName != '其它信息'
        }).map((item, index) => {
          const formData = {}
          item.customFieldList.forEach(item => {
            if (item.fieldType == 'ImageUpload' || item.fieldType == 'FileUpload' || item.fieldType == 'jobMultiple') {
              formData[item.id] = item.customDataRespVO?.fieldValue ? JSON.parse(item.customDataRespVO.fieldValue) : []
            } else if (item.fieldType == 'DeptPicker') {
              formData[item.id] = item.customDataRespVO?.fieldValue ? this.getTreeData(item.customDataRespVO.fieldValue) : []
            } else {
              formData[item.id] = item.customDataRespVO?.fieldValue
            }
          })
          return {
            ...item,
            formData
          }
        })

        console.log('this.subData', this.subData)
      } finally {
        this.loading = false
      }
    },

    handleEdit() {
      this.isShow = 2
    },
    getPostList() {
      PostApi.getSimplePostList().then((res) => {
        this.postList = res
      })
    },
    getDeptList() {
      DeptApi.getSimpleDeptList().then((res) => {
        this.deptList = res
      })
    },
    getForms(e) {
      const newArr = [];
      e.forEach((item) => {
        newArr.push({
          id: item.id,
          // name: item.fieldType,
          name: item.fieldType == 'Date' ? 'DateTime' : item.fieldType,
          title: item.fieldName,
          props: {
            required: item.required == 0 ? true : false,
            placeholder: item.hint,
            // options: this.getOptions(item.props),
            options: item.fieldType == 'jobMultiple' ? this.postList : this.getOptions(item.props),
            format: item.fieldType == 'Date' ? 'YYYY-MM-DD' : 'YYYY-MM-DD',
            multiple: item.fieldType == 'DeptPicker' ? true : ''
          },
        });
      });
      return newArr
    },
    getOptions(e) {
      const newArr = [];
      if (e) {
        let newE = JSON.parse(e)
        newE.options.forEach((item) => {
          newArr.push(item.label)
        })
      }
      return newArr
    },
    handleSubmit() {
      const promiseList = []
      console.log(this.subData, 'this.subData')
      this.subData.forEach((item, index) => {
        promiseList.push(
          new Promise((resolve, reject) => {
            console.log(this.$refs[`formRef` + index], ' this.$refs[`formRef` + index]')
            this.$refs[`formRef` + index][0].validate((validForm) => {
              if (validForm) {
                console.log('true')
                resolve()
              } else {
                console.log('false')
                reject()
              }
            })
          })
        )
      })
      Promise.all(promiseList).then(() => {
        console.log('成功')
        this.ergodicData()
      }).catch(() => {
        console.log('失败')
        return this.$message.warning("请完善必填项")
      })
    },
    ergodicData() {
      console.log(this.subData, 'this.subData')
      const arr = [...this.subData]
      const newArr = []
      arr.forEach((item) => {
        for (let key in item.formData) {
          for (let c of item.customFieldList) {
            if (key == c.id) {
              let newFieldVale = item.formData[key] ? item.formData[key] : ''
              if (c.fieldType == 'FileUpload' || c.fieldType == 'ImageUpload' || c.fieldType == 'jobMultiple') {
                newFieldVale = item.formData[key] ? JSON.stringify(item.formData[key]) : ''
              }
              if (c.fieldType == 'DeptPicker') {
                if (item.formData[key]) {
                  const a = []
                  item.formData[key].forEach((cIndex) => {
                    if (cIndex.id) {
                      a.push(cIndex.id)
                    }
                  })
                  const a2 = a.map(Number);
                  newFieldVale = JSON.stringify(a2)
                }
              }
              newArr.push({
                id: c.customDataRespVO ? c.customDataRespVO.id : null,
                instanceId: c.customDataRespVO ? c.customDataRespVO.instanceId : null,
                fieldId: key,
                fieldKey: c.fieldCode,
                fieldValue: newFieldVale,
                fieldName: c.fieldName,
                fieldType: c.fieldType,
                isJson: true
              })
            }
          }
        }
      })
      console.log(newArr, 'aa')
      this.submitForm(newArr)
      this.loading = true

    },
    async submitForm(newArr) {
      const mergedArray = [...newArr, ...JSON.parse(JSON.stringify(this.otherList))]
      console.log(mergedArray)
      // return
      if(this.isPush){
        UserRosterApi.pushContractCustomPersonData(mergedArray).then((res) => {
          if (res.code == 0) {
            this.$message.success('编辑成功')
            this.isShow = 1
            this.getUserInfo()
            this.loading = false
            this.$emit('updateParent');
            return 
          } else {
            this.loading = false
            return this.$message.error(res.msg)

          }
        }).catch((e) => {
          this.loading = false
          console.log(e, 'errrr')
        })

      }else{
        UserRosterApi.saveCustomPersonData(mergedArray).then((res) => {
          if (res.code == 0) {
            this.$message.success('修改成功')
            this.isShow = 1
            this.getUserInfo()
            this.loading = false
          } else {
            this.loading = false
            return this.$message.error(res.msg)

          }
        }).catch((e) => {
          this.loading = false
          console.log(e, 'errrr')
        })
      }
     
    },
    handelReset() {
      this.isShow = 1
      this.subData = []
      this.getUserInfo()
      // resetFields 方法不存在
    },
    filterImages(attachments) {
      return (attachments || []).filter((f) => f.isImage).map((f) => {
        return this.$getRes(f.url)
      })
    },
    filterFiles(attachments) {
      return (attachments || []).filter((f) => !f.isImage).map((f) => {
        return { ...f, url: f.url }
      })
    },
    download(file) {
      window.open(`${this.$getRes(file.url)}?name=${file.name}`, '_blank')
    },

    toParse(e, type) {
      const arr = JSON.parse(e)
      let newArr = []
      if (type == 'jobMultiple') {
        this.postList.forEach((item) => {
          arr.forEach((my) => {
            if (my == item.id) {
              newArr.push(item.name)
            }
          })
        })
      }
      if (type == 'DeptPicker') {
        this.deptList.forEach((item) => {
          arr.forEach((my) => {
            if (my == item.id) {
              newArr.push(item.name)
            }
          })
        })
      }
      return newArr.join(',')
    },
    getTreeData(data) {
      const arr = JSON.parse(data)
      console.log(data, 'dataaaaaaaaaaa')
      let newArr = []
      this.deptList.forEach((item) => {
        arr.forEach((c) => {
          if (item.id == c) {
            newArr.push({
              name: item.name,
              id: item.id,
              type: 'dept'
            })
          }
        })
      })
      console.log(data, 'data')
      return newArr
      // for (let i = 0; i < data.length; i++) {

      // }
    }
  },
}
</script>

<style lang="less" scoped>
.myAvatar {
  :deep(.a-img>div) {
    border-radius: 12px;
    background: #3370ff;
    font-size: 20px;
  }

  :deep(.name) {
    display: none;
  }
}

.topDiv {
  text-align: center;
  display: grid;
  justify-content: center;
  margin: 20px 0 10px;
  position: relative;
}

.nameP {
  position: relative;
  font-size: 20px;
  margin-bottom: 0;

  .nameSpan {
    cursor: pointer;
  }
}

.tagMy {
  position: absolute;
  margin-left: 10px;
  top: 4px;
}

.form-title {
  font-size: 16px;
  font-weight: 700;
  height: 38px;
  line-height: 38px;
  margin-bottom: 30px;
  width: 100%;

  .title {
    background: #fff;
    color: #303133;
    float: left;
    font-size: 16px;
    font-weight: 700;
    padding: 0 10px 0 0;
  }

  .line {
    border-bottom: 1px solid #ebebeb;
    padding-top: 18px;
  }
}

.cBox {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-bottom: 40px;

  .forDiv {
    width: 370px;
    display: flex;
    align-items: baseline;
    margin-right: 20px;
    margin-bottom: 20px;
    font-size: 15px;
    color: #5c5c5c;
  }

  span {
    display: inline-block;
  }

  .span1 {
    width: 100px;
    margin-right: 10px;
    position: relative;
  }

  .span1::after {
    content: '：';
    position: absolute;
    margin-left: 4px;
  }

  .span2 {
    flex: 1;
    word-wrap: break-word;
    width: 260px;
  }
}

.topBtn {
  position: absolute;
  right: 0;
}

.forms {
  width: 50%;
}

.file-comment {
  cursor: pointer;

  .file-item {
    width: 260px;
  }

  .file-item:hover {
    color: #1989fa
  }
}

.image-comment {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;

  .el-image {
    width: 95px;
    height: 60px;
    display: table-cell;
    padding-right: 5px;
    padding-bottom: 5px;
  }
}

:deep(.el-page-header__back) {
  display: none;
}

:deep(.el-divider) {
  display: none;
}
</style>