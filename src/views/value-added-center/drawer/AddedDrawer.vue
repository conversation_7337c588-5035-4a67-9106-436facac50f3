<template>
  <div>
    <el-drawer class="custom-detail-header" v-model="drawerVisible" size="34%" modal>
      <template #header="{ titleId }">
        <div :id="titleId" class="custom-drawer-header">
          <div class="click-area" @click="drawerVisible = false">
            <el-icon class="gray-text">
              <ArrowLeft />
            </el-icon>
            <span>已添加应用</span>
            <span class="gray-text">（{{ appList.length }}）</span>
          </div>
        </div>
      </template>
      <div class="all-section">
        <div class="all-list">
          <div v-for="(item, index) in appList" :key="index" class="all-list-cell">
            <el-image :src="item.appIcon" fit="cover"></el-image>
            <div class="item-info-area">
              <span class="item-name">{{ item.name }}</span>
              <span class="item-desc">{{ item.appDesc }}</span>
            </div>
            <el-button size="small" :disabled="item.isAdded" :loading="currentEditingItem === item"
              @click="onDelete(item)">删除</el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { AppItem } from '../index.vue'
import { ArrowLeft } from '@element-plus/icons-vue'
import { delUserWorkApp } from '@/api/common'

let myAppList = inject<AppItem[]>('myAppList') ?? <AppItem[]>[]

const message = useMessage() // 消息弹窗
const drawerVisible = ref(false)
const appList = ref(myAppList);
const currentEditingItem = ref<AppItem | null>()

// 更新 AddDrawer 数据
const updateAppDrawer = inject<() => void>('updateAddDrawer')
// 更新 增值中心 数据
const updateAppCenter = inject<() => void>('updateAppCenter')

// 删除
const onDelete = async (item: AppItem) => {
  try {
    currentEditingItem.value = item
    const res = await delUserWorkApp(item.id)
    if (res.code === 0) {
      appList.value = appList.value.filter((e) => e.id !== item.id)
      if (updateAppDrawer) updateAppDrawer()
      if (updateAppCenter) updateAppCenter()
      message.success('删除成功')
    } else {
      message.error('删除失败')
    }
  } finally {
    currentEditingItem.value = null
  }
}

// 提供 open 方法，用于打开弹窗
const openDrawer = async () => {
  drawerVisible.value = true
}
defineExpose({ openDrawer })

</script>

<style scoped>
.custom-drawer-header {
  .click-area {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 35%;

    .el-icon {
      margin-right: 5px;
    }

    .gray-text {
      color: #909399;
    }
  }
}

.all-section {
  margin: 30px 20px 40px 20px;

  .all-list {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 30px;

    .all-list-cell {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .el-image {
        width: 50px;
        height: 50px;
        border-radius: 20%;
        overflow: hidden;
      }

      .item-info-area {
        flex: 1;
        margin-left: 15px;
        display: flex;
        flex-direction: column;
        gap: 6px;

        .item-name {
          height: 22px;
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }

        .item-desc {
          height: 17px;
          font-size: 12px;
          color: #909399;
        }
      }

      .el-button {
        width: 60px;
        margin-left: 50px;
        border: 1px solid #ECEDED;
        font-size: 12px;
        color: #FF5757;
      }
    }
  }
}
</style>
