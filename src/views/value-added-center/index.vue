<template>
  <div class="center-view flex-column">
    <el-card class="top-card" shadow="never">
      <!-- banner -->
      <div class="center-align" style="justify-content: space-between;"> 
        <div class="title"> 工作中心 </div>
        <el-button link @click="toAppCenter" style="color: #3370FF; font-size: 14px;"> 应用中心 </el-button>
      </div>
      <div style="margin-top: 24px">
        <img @click="toYQB()" style="width: 100%; cursor: pointer" src="@/assets/image/yqb_bg.png" />
      </div>
      <!-- 最近使用 -->
      <div class="center-align" style="justify-content: space-between; margin-top: 45px">
        <div class="title"> 最近使用 </div>
        <div v-if="historyList.length" @click="delAppHistory()" class="center-align operation-btn">
          <img src="@/assets/image/del_1.png" />
          清空
        </div>
      </div>
      <div v-if="historyList.length > 0" class="center-align" style="margin-top: 24px; flex-wrap: wrap">
        <div v-for="(item, index) in historyList" :key="index" @click="goInfo(item, item.applicationId)"
          v-show="isValidUrl(item)" class="app-item center-align">
          <img class="logo-left" :src="item.appIcon ? item.appIcon : logoBlue" />
          <div class="app-title textover2">
            {{ item.name }}
          </div>
        </div>
      </div>
      <div v-else class="center-align default-none">
        <img src="@/assets/image/payslip-bg.png"></img>
        <div class="text-area">
          <span>暂无最近访问应用，</span>
          <div class="check-area" @click="toAppCenter">
            <span>去查看</span>
            <el-icon>
              <ArrowRight />
            </el-icon>
          </div>
        </div>
      </div>
      <div class="line"></div>
      <!-- 我的应用 -->
      <div class="center-align" style="justify-content: space-between; margin-top: 30px">
        <div class="title"> 我的应用 </div>
        <div class="center-align operation-btn" @click="toCollect">
          <img src="@/assets/image/collect_1.png" />
          收藏
        </div>
      </div>
      <div class="center-align" style="margin-top: 24px; flex-wrap: wrap">
        <div class="app-item center-align" 
          @click="goLocation()">
          <img class="logo-left" src="@/assets/image/location.png" />
          <div class="app-title textover2">
            实时定位
          </div>
        </div>

        <div class="app-item center-align" v-for="(item, index) in myAppList" :key="index" v-show="isValidUrl(item)"
          @click="goInfo(item, item.applicationId)">
          <img class="logo-left" :src="item.appIcon ? item.appIcon : logoBlue" />
          <div class="app-title textover2">
            {{ item.name }}
          </div>
        </div>
      </div>
      <!-- <div v-else class="center-align default-none">
        <img src="@/assets/image/payslip-bg.png"></img>
        <div class="text-area">
          <span>暂无我的应用，</span>
          <div class="check-area" @click="toAppCenter">
            <span>去查看</span>
            <el-icon>
              <ArrowRight />
            </el-icon>
          </div>
        </div>
      </div> -->
    </el-card>
    <!-- 生活中心 -->
    <el-card class="bottom-card" shadow="never">
      <div class="center-align" style="justify-content: space-between; margin-top: 10px">
        <div class="title"> 生活中心 </div>
        <div class="center-align operation-btn" @click="toLifeCenter">
          <img src="@/assets/image/fileCloudLayout1.png" />
          更多
        </div>
      </div>
      <div class="center-align" style="margin-top: 24px; flex-wrap: wrap">
        <div v-for="(item, index) in lifeAppList" :key="index" class="app-item center-align"
          @click="openUrl(item)">
          <img class="logo-left" :src="item.appIcon ? item.appIcon : logoBlue" />
          <div class="app-title textover2">
            {{ item.appName }}
          </div>
        </div>
      </div>
    </el-card>
    <!-- 添加应用抽屉 -->
    <AddDrawer ref="addDrawerRef" />
  </div>
</template>

<script lang="ts" setup>
import logoBlue from '@/assets/image/appLogo.png'
import { useRouter } from 'vue-router'
import { ArrowRight } from '@element-plus/icons-vue'
import {
  getUserAppHistoryList,
  getUserAppList,
  getAppList,
  delUserWorkAppHistory,
  addUserWorkAppHistory,
  getLifeCenterAppList
} from '@/api/common'
import AddDrawer from './drawer/AddDrawer.vue'

// AppItemModel
export type AppItem = {
  id: number,
  applicationId: number,
  appIcon: string,
  name: string,
  appName: string,
  appDesc: string,
  homepageLink: string,
  pcHomepageLink: string
  isAdded: boolean
}

const message = useMessage() // 消息弹窗
const router = useRouter()

const historyList = ref(<any>[]) // 最近使用列表
const myAppList = ref(<any>[]) // 我的应用列表
const allAppList = ref(<any>[]) // 全部应用列表
const lifeAppList = ref(<any>[]) // 生活应用列表

const addDrawerRef = ref<InstanceType<typeof AddDrawer>>()

// 最近使用列表
const getHistoryList = async () => {
  try {
    const res = await getUserAppHistoryList({
      current: 1,
      size: 6
    })
    historyList.value = res.data
  } finally {
  }
}
// 我的应用列表
const getMyAppList = async () => {
  try {
    const res = await getUserAppList({
      current: 1,
      size: 12
    })
    myAppList.value = res.data.list
  } finally {
  }
}
// 全部应用列表
const getAllAppList = async () => {
  try {
    const res = await getAppList({
      pageNo: 1,
      pageSize: 100
    })
    allAppList.value = res.data.list
  } finally {
  }
}

// 生活应用列表 getLifeCenterAppList
const getLifeCenterApp = async () => {
  try {
    const res = await getLifeCenterAppList()

    let lifeList = <any>[]
    for (let i = 0; i < res.data.length; i++) {
      if (res.data[i].applications) {
        for (let x = 0; x < res.data[i].applications.length; x++) {
          if (isValidUrl(res.data[i].applications[x])) {
            lifeList.push(res.data[i].applications[x])
          }
        }
      }
    }

    lifeAppList.value = lifeList
  } finally {
  }
}

// 前往易企帮
const toYQB = () => {
  router.push('/yqb/yqb-index')
}

// 前往应用中心
const toAppCenter = () => {
  router.push('/appCenter')
}

// 前往生活中心
const toLifeCenter = () => {
  router.push('/lifeCenter')
}

const isValidUrl = (item: any) => {
  const trimmedUrl = item.pcHomepageLink.replace(/^https:\/\/\s*/, '').trim()
  return !!trimmedUrl
}

// 应用中心 - 打开应用
const goInfo = async (item, applicationId) => {
  if (applicationId) {
    await addUserWorkAppHistory({
      applicationId: applicationId
    })
  }

  router.push({
    path: '/talentBoLeLink/' + item.id,
    query: { link: encodeURIComponent(item.pcHomepageLink), names: item.name || item.appName }
  })
}

// 生活中心 - 打开应用
const openUrl = async (item: any) => {
  window.open(item.pcHomepageLink, "_blank");
}

// 清空
const delAppHistory = async () => {
  try {
    const res = await delUserWorkAppHistory()
    if (res.code === 0) {
      message.success('清空成功')
      getHistoryList()
    } else {
      message.error('清空失败')
    }
  } finally {
  }
}

// 收藏
const toCollect = () => {
  addDrawerRef.value?.openDrawer()
}

// 提供 更新页面方法 给 AddDrawer，添加、删除应用成功时更新
provide('updateAppCenter', () => {
  getHistoryList()
  getMyAppList()
})

onMounted(() => {
  getHistoryList()
  getMyAppList()
  getAllAppList()
  getLifeCenterApp()
})


// 前往实时定位
const goLocation = () => {
  router.push('/location')
}


</script>

<style lang="less" scoped>
.title {
  font-weight: 500;
  font-size: 16px;
  color: #303133;
  line-height: 22px;
}

.app-item {
  margin-right: 30px;
  cursor: pointer;
  margin-bottom: 30px;
}

.app-title {
  font-size: 12px;
  color: #303133;
  line-height: 17px;
  width: 96px;
}

.logo-left {
  width: 36px;
  height: 36px;
  margin-right: 8px;
  border-radius: 8px;
}

.line {
  height: 1px;
  background: #eceded;
}

.flex-column {
  flex-direction: column;
}

.top-card {
  width: 1120px;
  margin-bottom: 20px;

  .default-none {
    margin: 20px 0 30px 0;
    display: flex;
    flex-direction: column;
    font-size: 13px;

    img {
      width: 100px;
    }

    .text-area {
      margin-top: 10px;
      display: flex;

      .check-area {
        display: flex;
        align-items: center;
        color: #3370FF;
        cursor: pointer;
      }
    }
  }
}

.bottom-card {
  width: 1120px;
}

.operation-btn {
  font-size: 12px;
  color: #303133;
  line-height: 16px;
  cursor: pointer;

  img {
    width: 16px;
    height: 16px;
    margin: 0 5px
  }
}
</style>
