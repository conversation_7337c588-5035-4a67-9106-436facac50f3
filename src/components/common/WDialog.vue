<template>
  <el-dialog :class="{ 'custom-dialog': true, border: border, fullscreen: fullscreen }" :width="width" :title="title"
    append-to-body :close-on-click-modal="clickClose" @opened="$emit('opened')" @closed="$emit('closed')"
    :destroy-on-close="closeFree" v-model="_value">
    <template #header>
      <slot name="title"></slot>
    </template>
    <slot></slot>
    <template #footer>
      <div v-if="showFooter">
        <el-button size="default" @click="_value = false; $emit('cancel')">{{ cancelText }}</el-button>
        <el-button size="default" @click="$emit('ok', 'otherEvent')" v-if="needOther">{{ otherText }}</el-button>
        <el-button size="default" :icon="okLoading ? 'el-icon-loading' : ''" :disabled="okLoading" type="primary"
          @click="$emit('ok')">{{ okText }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'WDialog',
  install(Vue) {
    window.$vueApp.component('WDialog', this)
  },
  components: {},
  props: {
    title: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '50%',
    },
    fullscreen: {
      type: Boolean,
      default: false,
    },
    noPadding: {
      type: Boolean,
      default: false,
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    clickClose: {
      type: Boolean,
      default: false,
    },
    closeFree: {
      type: Boolean,
      default: false,
    },
    showFooter: {
      type: Boolean,
      default: true,
    },
    cancelText: {
      type: String,
      default: '取 消',
    },
    okText: {
      type: String,
      default: '确 定',
    },
    okLoading: {
      type: Boolean,
      default: false,
    },
    border: {
      type: Boolean,
      default: true,
    },
    needOther: {//多一个自定义 按钮事件指令 目前用于审批确认/拒绝时弹窗内：“确定并批阅下一份”
      type: Boolean,
      default: false,
    },
    otherText: {
      type: String,
      default: '',
    },
  },
  emits: ['opened', 'closed', 'cancel', 'ok', 'update:modelValue'],
  data() {
    return {}
  },
  computed: {
    _value: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      },
    },
  },

  methods: {},

}
</script>

<style lang="less">
.custom-dialog {
  .el-dialog__header {
    margin-right: 0 !important;
    border-bottom: 1px solid #ebeef5 !important;
    padding: 15px 20px !important;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__headerbtn .el-dialog__close {
    font-weight: bold
  }

  .el-dialog__footer {
    padding: 15px 20px !important;
    border-top: 1px solid #ebeef5;
  }
}

.border {
  .el-dialog__footer {
    padding: 15px 20px !important;
    border-top: 1px solid #e8e8e8;
  }
}

.fullscreen {
  overflow: hidden;
  margin-top: 0 !important;
  width: 100% !important;

  .el-dialog__body {
    padding: 0;
    height: calc(100vh - 94px);
  }
}
</style>
