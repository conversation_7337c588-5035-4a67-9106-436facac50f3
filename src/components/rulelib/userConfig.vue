<template>
  <Dialog title="权限管理" v-model="dialogVisible" width="400">
    <div v-for="(item, index) in checkList" :key="index">
      <div class="checkItem">
        <div class="left">
          <avatar :name="item.name ? item.name.slice(-1) : ''" :src="''" :size="32" :title="item.name">
          </avatar>
          <div class="nameRight">
            <span>{{ item.name }}</span>
          </div>
        </div>
        <div class="right" @click="handleConfig">
          <el-dropdown @command="(command) => handleCommand(command, item)" trigger="click">
            <span class="dropSpan">
              {{ item.perm == 1 ? '可管理' : item.perm == 2 ? '可编辑' : '可查看/下载' }}<arrow-down />
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="1" :icon="Setting">
                  <div class="itemDiv">
                    <p>可管理</p>
                    <p>可编辑/上传/下载/删除</p>
                  </div>
                </el-dropdown-item>
                <el-dropdown-item command="2" :icon="Edit">
                  <div class="itemDiv">
                    <p>可编辑</p>
                    <p>可编辑/上传/下载</p>
                  </div>
                </el-dropdown-item>
                <el-dropdown-item command="3" :icon="Download">
                  <div class="itemDiv">
                    <p>可查看/下载</p>
                    <p>可下载</p>
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleOk">确 定</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { Setting, Edit, Download, ArrowDown } from '@element-plus/icons-vue'
import { rulelibApi } from '@/api/rulelib/list'
const { t } = useI18n()
const message = useMessage()
const dialogVisible = ref(false)


const checkList = ref<any>([])

const classId = ref(undefined)

const open = async (user,classIdVal) => {
  classId.value = classIdVal
  checkList.value = []
  arr.value = []
  
  user.forEach(item => {
    checkList.value.push({ ...item, perm: 3 })
  })
  dialogVisible.value = true
}
const handleConfig = () => {

}
// 更多下拉
const handleCommand = (i, item) => {
  handlePermission(i, item)
}
const handlePermission = (i, item) => {
  item.perm = i
}


const arr = ref([
  {
    classId: undefined,
    userId: undefined,
    perm: undefined,
    
  }
])
const handleOk = async () => {
  arr.value = []
  checkList.value.forEach((item) => {
    arr.value.push({
      classId: classId.value,
      userId: item.id,
      perm: item.perm,
      
    })
  })
  console.log(arr)
  try {
    await rulelibApi.permissionUpdate(arr.value)
    message.success('添加成功')
    dialogVisible.value = false
    emit('success')
  } finally {

  }
}



defineExpose({ open })
const emit = defineEmits(['success'])





</script>
<style scoped lang="less">
:deep(.el-dialog__body) {
  max-height: 400px;
  overflow-y: auto;
}

.checkItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  .left {
    display: flex;
    align-items: center;

    :deep(.avatar) {
      margin-right: 12px;
    }

    :deep(.avatar .a-img > div) {
      border-radius: 4px;
      font-size: 14px;
      background: #3370FF;
    }

    :deep(.avatar .name) {
      display: none;
    }

    .nameRight {
      span {
        display: block;

      }

      span:nth-child(1) {
        margin-bottom: 3px;
      }

      span:nth-child(2) {
        font-size: 12px;
        color: #A2A3A5;
      }
    }
  }

  .right {
    height: 32px;
    align-items: center;
    display: flex;
    justify-content: end;
    margin-left: 100px;

    .dropSpan {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      cursor: pointer;

      svg {
        width: 14px;
        margin-left: 4px;
      }
    }

    .dropSpan:hover::before {
      content: '';
      position: absolute;
      left: -8px;
      right: -8px;
      bottom: -6px;
      top: -6px;
      background: #F3F4F7;
      border-radius: 4px;
      z-index: -1;
    }
  }
}

.itemDiv {
  display: block;
  width: 120px;

  // margin-bottom: 20px;
  p {
    margin: 0;
    padding: 0;
  }

  p:nth-child(2) {
    position: absolute;
    font-size: 12px;
  }
}


:deep(.el-dropdown-menu__item) {
  padding-bottom: 30px;
}


:deep(.el-dropdown-menu__item:not(.is-disabled):focus) {
  background: #F3F4F7;
  color: #303133;
}
</style>