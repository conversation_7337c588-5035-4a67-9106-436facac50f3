import type { App } from 'vue'


export function clickOutside(app: App<Element>) {
  
  app.directive('click-out-side', {
    beforeMount(el, binding) {
      el.clickOutsideEvent = function (event) {
        if (!(el === event.target || el.contains(event.target))) {
          binding.value(event);
        }
      };
    },
    mounted(el, binding) {
      setTimeout(() => {
        document.addEventListener("click", el.clickOutsideEvent);
      }, 100); // 延迟100毫秒绑定事件
    },
    unmounted(el) {
      document.removeEventListener("click", el.clickOutsideEvent);
    },
  })
}
