<script lang="ts" setup>

import * as commonApi from '@/api/common'

const phoneQrCodeUrl = ref('')
const winUrl = ref('')
const macUrl = ref('')
commonApi.getClientUrl().then(res => {    
  console.log('getClientUrl res=',res.data)
  for(let  i=0;i<res.data.length;i++){
    if(res.data[i].type == "banban_client_url_mobile"){
      for(let x=0;x<res.data[i].platform.length;x++){
        phoneQrCodeUrl.value=res.data[i].platform[x].url
      }
    }
    if(res.data[i].type == "banban_client_url_pc"){
      for(let x=0;x<res.data[i].platform.length;x++){
        if(res.data[i].platform[x].name=="Windows"){
          winUrl.value=res.data[i].platform[x].url
        }
        if(res.data[i].platform[x].name=="MacOS"){
          macUrl.value=res.data[i].platform[x].url
        }
      }
    }
  }
}).catch(err => {
  console.log('getClientUrl err=',err)
})


function dwClick(url: any) { 
  window.open(url)
}


defineOptions({ name: 'dwCard' })




defineProps({
  
})

const mobileTerminalType = ref(1)


function mobileTerminalTypeChange(type: number) { 
  mobileTerminalType.value = type
}

</script>

<template>
<div class="center-view" style="margin-left: 10px;">
  <el-popover
    placement="bottom"
    title=""
    :width="360"
    trigger="click"
    popper-style="padding: 0;"
  >
    <template #reference>
      <div class="center-view" style="width: 24px;height: 24px;cursor: pointer;">
        <img  style="width:20px;height:20px;" src="@/assets/image/dwCard.svg"  />
      </div>
    </template>
    <div style="display: flex;">
            <div style="width: 160px;">
              <div style="height: 120px;display: flex;cursor: pointer;" @click="mobileTerminalTypeChange(1)">
                <div v-if="mobileTerminalType == 1" style="width: 4px;height: 120px;background: #2164EB;"></div>
                <div v-if="mobileTerminalType != 1" style="width: 4px;height: 120px;background: #F3F4F5;"></div>
                <div class="center-align" style="flex:1;background: #F3F4F5;color: #333;"
                  :style="'background: ' + (mobileTerminalType == 2 ? '#F3F4F5;' : '#fff;')">
                  <img style="width: 36px;height: 36px;margin-left: 20px;margin-right: 16px;"
                    src="@/assets/image/phone.png" />
                  移动端
                </div>
              </div>
              <div style="height: 120px;display: flex;cursor: pointer;" @click="mobileTerminalTypeChange(2)">
                <div v-if="mobileTerminalType == 2" style="width: 4px;height: 120px;background: #2164EB;"></div>
                <div v-if="mobileTerminalType != 2" style="width: 4px;height: 120px;background: #F3F4F5;"></div>
                <div class="center-align" style="flex:1;background: #F3F4F5;color: #333;"
                  :style="'background: ' + (mobileTerminalType == 1 ? '#F3F4F5;' : '#fff;')">

                  <img style="width: 36px;height: 36px;margin-left: 20px;margin-right: 16px;"
                    src="@/assets/image/pc.png" />

                  电脑端
                </div>
              </div>
            </div>
            <div v-if="mobileTerminalType == 1" class="center-view" style="width: 200px;flex-flow: column;">
              <img key="phone_h5" 
                style="width: 136px;height: 136px;" :src="phoneQrCodeUrl" />
              <div style="color: #999;margin-top: 12px;"> 扫码下载 iOS/Android</div>
            </div>
            <div v-if="mobileTerminalType == 2" class="center-view" style="width: 200px;flex-flow: column;">
                <div class="center-view" style="margin-bottom: 10px;">
                  <img style="width: 30px;height: 30px;margin-right: 5px;" src="@/assets/image/win.png"  />
                  <div style="font-size: 14px;color: #333333;line-height: 20px;">
                    Windows
                  </div>
                </div>
                <div class="center-view" style="margin-bottom: 25px;">
                    <div @click="dwClick(winUrl)" class="center-view download-button" style="width: 136px;height: 32px;border-radius: 4px;border: 1px solid #DCDFE6;">
                      <div class="download-icon"  > </div>
                      <div style="font-size: 12px;line-height: 17px;">
                             立即下载
                      </div>
                    </div>
                </div>

                <div class="center-view" style="margin-bottom: 25px;">
                  <div style="width: 160px;height: 1px;border: 1px solid #DCDFE6;"></div>
                </div>

                <div class="center-view" style="margin-bottom: 10px;">
                  <img style="width: 30px;height: 30px;margin-right: 5px;" src="@/assets/image/ios.png"  />
                  <div style="font-size: 14px;color: #333333;line-height: 20px;">
                    MacOS
                  </div>
                </div>
                <div class="center-view" style="">
                    <div @click="dwClick(macUrl)" class="center-view download-button" style="width: 136px;height: 32px;border-radius: 4px;border: 1px solid #DCDFE6;">
                      <div class="download-icon"  > </div>
                      <div style="font-size: 12px;line-height: 17px;">
                             立即下载
                      </div>
                    </div>
                </div>



            </div>
          </div>
  </el-popover>
</div>

</template>
<style scoped>

.download-button {
    background-color: #ffffff; /* 初始背景颜色 */
    transition: background-color 0.3s; /* 平滑过渡效果 */
    color: #333333; /* 字体颜色 */
    cursor: pointer;
}

.download-button:hover {
  background: #3370FF; /* 悬浮时的背景颜色 */
  color: #fff; /* 字体颜色 */
}

.download-button .download-icon {
    transition: opacity 0.3s; /* 平滑过渡效果 */
    
    background-image: url('@/assets/image/dw.png'); /* 悬浮时的图标 */
    width: 11px; /* 确保新图标的尺寸与原图标一致 */
    height: 14px;
    margin-top:-2px;
    margin-right: 8px;
    
}

.download-button:hover .download-icon {
  
    background-image: url('@/assets/image/dw2.png'); /* 悬浮时的图标 */
    width: 11px; /* 确保新图标的尺寸与原图标一致 */
    height: 14px;
    margin-top:-2px;
    margin-right: 8px;
    
}





</style>
