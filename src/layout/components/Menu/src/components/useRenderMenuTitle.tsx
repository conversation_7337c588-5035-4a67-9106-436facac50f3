import type {RouteMeta} from 'vue-router'
import {Icon} from '@/components/Icon'
import {useI18n} from '@/hooks/web/useI18n'
import {computed, ref} from 'vue';
import {useAppStore} from '@/store/modules/app'

const appStore = useAppStore()

const allUnReadCount = computed(() => appStore.getAllUnReadCount);

import {useMyStore} from "@/store/modules/jump";

const store = useMyStore()

const badgeShowStyle = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: '4px'
}

const badgeHideStyle = {
    display: 'none',
}

export const useRenderMenuTitle = () => {
    const renderMenuTitle = (meta: RouteMeta) => {
        const {t} = useI18n()
        const {title = 'Please set title', icon} = meta
        // console.log(meta)
        if (t(title as string) == '审批') {
            return icon ? (
                <>
                    <Icon icon={meta.icon}></Icon>
                    <span class="v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap">
                    {t(title as string)}
                </span>
                    <span>
                    <el-badge style={store.processNum > 0 ? badgeShowStyle : badgeHideStyle} color="#f00" value={store.processNum}/>
                </span>
                </>
            ) : (
                <>
                <span class="v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap">
                    {t(title as string)}
                </span>
                    <span>
                    <el-badge style={store.processNum > 0 ? badgeShowStyle : badgeHideStyle} color="#f00" value={store.processNum}/>
                </span>
                </>
            )
        } else if (t(title as string) == '待我处理') {
            return icon ? (
                <>
                    <Icon icon={meta.icon}></Icon>
                    <span class="v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap">
                    {t(title as string)}
                </span>
                    <span>
                    <el-badge style={store.todoNum > 0 ? badgeShowStyle : badgeHideStyle} color="#f00" value={store.todoNum}/>
                </span>
                </>
            ) : (
                <>
                <span class="v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap">
                    {t(title as string)}
                </span>
                    <span>
                    <el-badge style={store.todoNum > 0 ? badgeShowStyle : badgeHideStyle} color="#f00" value={store.todoNum}/>
                </span>
                </>
            )
        } else if (t(title as string) == '我收到的') {
            return icon ? (
                <>
                    <Icon icon={meta.icon}></Icon>
                    <span class="v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap">
                    {t(title as string)}
                </span>
                    <span>
                    <el-badge style={store.ccNum > 0 ? badgeShowStyle : badgeHideStyle} color="#f00" value={store.ccNum}/>
                </span>
                </>
            ) : (
                <>
                <span class="v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap">
                    {t(title as string)}
                </span>
                    <span>
                    <el-badge style={store.ccNum > 0 ? badgeShowStyle : badgeHideStyle} color="#f00" value={store.ccNum}/>
                </span>
                </>
            )
        } else {
            return icon ? (
                <>
                    <Icon icon={meta.icon}></Icon>
                    <span class="v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap">
                        {t(title as string)}

                       
                        {/* {title === '消息' && allUnReadCount.value > 0 && (
                            <div style={{
                                display: 'inline-flex',
                                alignItems: 'center',
                                marginLeft: '10px',
                                verticalAlign: 'middle'
                            }}>
                                <div style={{
                                    color: 'white',
                                    fontSize: '10px',
                                    backgroundColor: 'red',
                                    borderRadius: '8px',
                                    height: '16px',
                                    padding: '0 5px',
                                    lineHeight: '16px',
                                    fontStyle: 'normal',
                                    display: 'inline-block'
                                }}>
                                    {allUnReadCount.value > 99 ? '99+' : allUnReadCount.value}
                                </div>
                            </div>
                        )} */}
                    </span>
                    {title === '消息' && allUnReadCount.value > 0 && (
                           <span> 
                            <el-badge style={allUnReadCount.value > 0 ? badgeShowStyle : badgeHideStyle} color="#f00" value={allUnReadCount.value}/>
                            </span>
                    )}
                    
                </>
            ) : (
                <span class="v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap">
                {t(title as string)}
              </span>
            )
        }
    }

    return {
        renderMenuTitle
    }
}
